<template>
  <div :class="['app-card', { [`app-card-${variant}`]: variant }]">
    <div v-if="$slots.header" class="app-card-header">
      <slot name="header" />
    </div>
    <div class="app-card-body">
      <h3 v-if="title" class="app-card-title">{{ title }}</h3>
      <slot />
    </div>
    <div v-if="$slots.footer" class="app-card-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: '',
    validator: (value: string) => ['', 'primary', 'secondary', 'transparent'].includes(value)
  }
})
</script>

<style lang="scss" scoped>
.app-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: all $transition-base;
  height: 100%;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: $shadow-md;
    border-color: rgba($secondary-color, 0.3);
  }
  
  &-primary {
    border-top: 4px solid $primary-color;
    
    &:hover {
      border-color: $primary-color;
    }
  }
  
  &-secondary {
    border-top: 4px solid $secondary-color;
    
    &:hover {
      border-color: $secondary-color;
    }
  }
  
  &-transparent {
    background: transparent;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    border: none;
    
    &:hover {
      box-shadow: none;
      transform: none;
    }
  }
  
  &-header {
    padding: $spacing-md;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  &-body {
    padding: $spacing-lg;
  }
  
  &-footer {
    padding: $spacing-md;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  &-title {
    color: $secondary-color;
    font-size: $font-size-xl;
    margin-bottom: $spacing-md;
    font-weight: 600;
  }
}
</style> 