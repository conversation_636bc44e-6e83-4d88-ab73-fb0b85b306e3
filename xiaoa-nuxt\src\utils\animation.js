/**
 * 动画辅助函数，提供常用的动画相关工具方法
 */

// 检查元素是否在视口中
export const isElementInViewport = (el, offset = 0) => {
  if (!el) return false;
  
  if (typeof window === 'undefined') return false;
  
  const rect = el.getBoundingClientRect();
  
  return (
    rect.top + offset <= window.innerHeight &&
    rect.bottom >= offset &&
    rect.left <= window.innerWidth &&
    rect.right >= 0
  );
};

// 创建滚动动画监听器
export const createScrollAnimationObserver = (elements, animationFn, options = {}) => {
  if (typeof window === 'undefined') return null;
  
  const defaultOptions = {
    threshold: 0.1, // 元素可见比例
    once: true,     // 是否只执行一次
    rootMargin: '0px 0px -10% 0px', // 扩大或缩小观察范围
  };
  
  const mergedOptions = { ...defaultOptions, ...options };
  
  // 创建观察器
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // 调用动画函数
        animationFn(entry.target);
        
        // 如果只执行一次，取消观察该元素
        if (mergedOptions.once) {
          observer.unobserve(entry.target);
        }
      }
    });
  }, {
    threshold: mergedOptions.threshold,
    rootMargin: mergedOptions.rootMargin
  });
  
  // 开始观察元素
  if (Array.isArray(elements)) {
    elements.forEach(el => observer.observe(el));
  } else if (elements) {
    observer.observe(elements);
  }
  
  return observer;
};

// 创建滚动位置触发器
export const createScrollTrigger = (callback, options = {}) => {
  if (typeof window === 'undefined') return null;
  
  const defaultOptions = {
    offset: 100,     // 触发偏移量
    throttleDelay: 100, // 节流延迟时间
    direction: 'both', // 触发方向：'up', 'down', 'both'
  };
  
  const mergedOptions = { ...defaultOptions, ...options };
  
  let lastScrollPosition = window.pageYOffset;
  let ticking = false;
  
  const handleScroll = () => {
    const currentScrollPos = window.pageYOffset;
    const scrollDirection = currentScrollPos > lastScrollPosition ? 'down' : 'up';
    
    if (!ticking) {
      window.requestAnimationFrame(() => {
        // 检查滚动方向与配置的方向是否匹配
        if (
          mergedOptions.direction === 'both' ||
          mergedOptions.direction === scrollDirection
        ) {
          callback({
            scrollPosition: currentScrollPos,
            direction: scrollDirection,
            pastOffset: currentScrollPos > mergedOptions.offset
          });
        }
        
        lastScrollPosition = currentScrollPos;
        ticking = false;
      });
      
      ticking = true;
    }
  };
  
  window.addEventListener('scroll', handleScroll);
  
  // 返回清理函数
  return () => window.removeEventListener('scroll', handleScroll);
};

// 创建打字机效果
export const createTypewriterEffect = (element, text, options = {}) => {
  if (!element) return null;
  
  const defaultOptions = {
    speed: 50,          // 每个字符的打印延迟(ms)
    delay: 0,           // 开始前的延迟
    cursor: true,       // 是否显示光标
    cursorChar: '|',    // 光标字符
    cursorSpeed: 600,   // 光标闪烁速度(ms)
    onComplete: null    // 完成时的回调函数
  };
  
  const mergedOptions = { ...defaultOptions, ...options };
  
  // 准备元素
  const originalContent = element.innerHTML;
  element.innerHTML = '';
  
  // 如果开启光标，添加光标样式
  if (mergedOptions.cursor) {
    const style = document.createElement('style');
    const styleId = `typewriter-cursor-style-${Date.now()}`;
    
    style.id = styleId;
    style.textContent = `
      @keyframes typewriter-cursor-${styleId} {
        0% { opacity: 1; }
        50% { opacity: 0; }
        100% { opacity: 1; }
      }
      
      .typewriter-cursor-${styleId} {
        display: inline-block;
        animation: typewriter-cursor-${styleId} ${mergedOptions.cursorSpeed}ms infinite;
      }
    `;
    
    document.head.appendChild(style);
  }
  
  let currentIndex = 0;
  let cursorElement = null;
  
  if (mergedOptions.cursor) {
    cursorElement = document.createElement('span');
    cursorElement.className = `typewriter-cursor-${styleId}`;
    cursorElement.textContent = mergedOptions.cursorChar;
    element.appendChild(cursorElement);
  }
  
  // 延迟后开始打字
  setTimeout(() => {
    const typeNextChar = () => {
      if (currentIndex < text.length) {
        const char = text[currentIndex];
        const charElement = document.createTextNode(char);
        
        if (cursorElement) {
          element.insertBefore(charElement, cursorElement);
        } else {
          element.appendChild(charElement);
        }
        
        currentIndex++;
        setTimeout(typeNextChar, mergedOptions.speed);
      } else if (mergedOptions.onComplete) {
        mergedOptions.onComplete();
      }
    };
    
    typeNextChar();
  }, mergedOptions.delay);
  
  // 返回重置函数
  return () => {
    element.innerHTML = originalContent;
    if (document.getElementById(styleId)) {
      document.head.removeChild(document.getElementById(styleId));
    }
  };
};

// 创建平滑滚动函数
export const smoothScrollTo = (targetElement, options = {}) => {
  if (typeof window === 'undefined' || !targetElement) return;
  
  const defaultOptions = {
    duration: 800,
    offset: 0,
    easing: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1, // 缓动函数
    onComplete: null
  };
  
  const mergedOptions = { ...defaultOptions, ...options };
  
  // 获取目标位置
  const targetPosition = typeof targetElement === 'number' 
    ? targetElement 
    : targetElement.getBoundingClientRect().top + window.pageYOffset;
  
  const startPosition = window.pageYOffset;
  const distance = targetPosition - startPosition - mergedOptions.offset;
  let startTime = null;
  
  const animation = currentTime => {
    if (startTime === null) startTime = currentTime;
    const timeElapsed = currentTime - startTime;
    const progress = Math.min(timeElapsed / mergedOptions.duration, 1);
    const easedProgress = mergedOptions.easing(progress);
    
    window.scrollTo(0, startPosition + distance * easedProgress);
    
    if (timeElapsed < mergedOptions.duration) {
      requestAnimationFrame(animation);
    } else if (mergedOptions.onComplete) {
      mergedOptions.onComplete();
    }
  };
  
  requestAnimationFrame(animation);
};

// 导出模块
export default {
  isElementInViewport,
  createScrollAnimationObserver,
  createScrollTrigger,
  createTypewriterEffect,
  smoothScrollTo
}; 