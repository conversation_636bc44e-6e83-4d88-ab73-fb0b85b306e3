@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-sans bg-dark text-light overflow-x-hidden;
  }
}

@layer components {
  .section {
    @apply min-h-screen flex items-center justify-center py-24 relative overflow-hidden;
  }

  /* 导航栏样式 */
  .navbar {
    @apply fixed w-full z-50 left-1/2 -translate-x-1/2 top-0 py-5 bg-dark bg-opacity-30 backdrop-blur-lg transition-all duration-300;
  }

  .navbar-brand {
    @apply font-bold text-secondary;
  }

  .nav-link {
    @apply text-light mx-2.5 relative overflow-hidden transition-colors hover:text-secondary;
  }

  .nav-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-accent transition-all duration-300;
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  /* 英雄区域样式 */
  .hero-title {
    @apply text-5xl md:text-6xl font-extrabold mb-5 bg-gradient-text bg-clip-text text-transparent;
  }

  .hero-subtitle {
    @apply text-xl md:text-2xl mb-7;
  }

  .hero-btn {
    @apply bg-gradient-accent border-none py-3 px-8 rounded-full text-white font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-button;
  }

  /* 卡片样式 */
  .feature-card {
    @apply bg-white bg-opacity-5 backdrop-blur-md rounded-xl p-7 mb-7 transition-all duration-500 h-full border border-white border-opacity-10 hover:border-secondary hover:-translate-y-2.5 hover:shadow-card;
  }

  .feature-icon {
    @apply text-4xl mb-5 text-secondary;
  }

  .feature-title {
    @apply text-2xl font-semibold mb-4;
  }

  /* 数字人部分样式 */
  .section-title {
    @apply text-4xl font-bold mb-12 text-center;
  }

  .section-title span {
    @apply text-accent;
  }

  /* 分发系统样式 */
  .distribution-item {
    @apply flex mb-10;
  }

  .distribution-icon {
    @apply bg-gradient-accent w-[70px] h-[70px] rounded-full flex items-center justify-center mr-5 flex-shrink-0;
  }

  .distribution-icon i {
    @apply text-2xl text-white;
  }

  .distribution-text h3 {
    @apply text-2xl mb-2.5 text-secondary;
  }

  /* 智能体部分样式 */
  .agent-module {
    @apply bg-white bg-opacity-5 backdrop-blur-md rounded-xl p-6 text-center transition-all duration-500 border border-white border-opacity-10 hover:border-secondary hover:-translate-y-2.5 hover:shadow-card w-full sm:w-[280px];
  }

  .module-icon {
    @apply text-5xl mb-4 text-secondary;
  }

  /* 小程序/客户端部分样式 */
  .device {
    @apply relative bg-gray-800 rounded-xl shadow-lg overflow-hidden;
  }

  .device-mobile {
    @apply w-[300px] h-[600px];
  }

  .device-tablet {
    @apply w-[500px] h-[350px];
  }

  .device-desktop {
    @apply w-[600px] h-[400px];
  }

  .device-frame {
    @apply absolute top-5 left-5 right-5 bottom-5 bg-white rounded-lg flex items-center justify-center shadow-inner;
  }

  .platform-card {
    @apply bg-white bg-opacity-5 backdrop-blur-md rounded-xl p-6 text-center transition-all duration-500 border border-white border-opacity-10 hover:border-secondary hover:-translate-y-2.5 hover:shadow-card;
  }

  .benefit-card {
    @apply bg-white bg-opacity-5 backdrop-blur-md rounded-xl p-6 text-center transition-all duration-500 border border-white border-opacity-10 hover:border-secondary hover:-translate-y-2.5 hover:shadow-card;
  }

  .benefit-icon {
    @apply text-5xl mb-4 text-secondary;
  }

  /* 联系我们部分样式 */
  .contact-card {
    @apply flex items-center bg-white bg-opacity-5 backdrop-blur-md rounded-xl p-6 border border-white border-opacity-10;
  }

  .contact-icon {
    @apply text-4xl text-secondary mr-5 flex-shrink-0;
  }

  .contact-form {
    @apply bg-white bg-opacity-5 backdrop-blur-md rounded-xl p-10 border border-white border-opacity-10;
  }

  .contact-form .form-control, .contact-form .form-select {
    @apply bg-white bg-opacity-10 border border-white border-opacity-20 text-light py-4 px-5 rounded-lg mb-5 transition-all duration-300 focus:bg-white focus:bg-opacity-20 focus:border-secondary focus:shadow-lg focus:outline-none;
  }

  .contact-submit-btn {
    @apply bg-gradient-accent border-none py-3 px-8 rounded-full text-white font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-button;
  }

  /* 页脚样式 */
  .footer {
    @apply bg-dark py-16 relative border-t border-white border-opacity-10;
  }

  /* 自定义浮动元素类 */
  .floating-element {
    animation: float 6s ease-in-out infinite;
  }
}

/* 自定义动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-right {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

/* 预加载动画 */
.loader {
  @apply fixed inset-0 bg-dark flex justify-center items-center z-[9999];
}

.loader-circle {
  @apply w-[50px] h-[50px] border-4 border-white border-opacity-10 border-t-accent rounded-full mb-5 animate-spin;
} 