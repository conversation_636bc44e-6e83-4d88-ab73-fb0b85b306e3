# 小 A 引擎官网

## 项目介绍

小 A 引擎官网是基于 Nuxt.js 构建的现代化官方网站，展示数字人智能体系统的产品和服务，包括数字人技术、分发系统、智能体技术以及客户端解决方案。

## Tailwind CSS 迁移

本项目从原来的SCSS样式系统迁移到了Tailwind CSS，以实现更高效的开发和维护。

### 已完成的迁移工作

1. **创建了tailwind.config.js**
   - 配置了颜色主题，匹配原有设计系统
   - 添加了自定义动画、阴影和背景
   - 提供了详细的注释说明

2. **创建了基础CSS文件**
   - 设置了`main.css`作为Tailwind入口文件
   - 使用`@apply`指令定义了常用组件样式
   - 添加了自定义动画和过渡效果

3. **更新了Nuxt配置**
   - 移除了SCSS相关配置
   - 配置了Tailwind CSS模块
   - 移除了Bootstrap依赖，完全使用Tailwind

4. **更新了布局组件**
   - 重构了`default.vue`布局
   - 使用Tailwind类替换了所有SCSS样式
   - 添加了响应式设计

5. **更新了全局组件**
   - 重构了`TheHeader.vue`导航栏组件
   - 重构了`LoadingAnimation.vue`加载动画组件
   - 重构了`TheFooter.vue`页脚组件
   - 移除了冗余的SCSS样式文件

6. **更新了首页组件**
   - 重构了`index.vue`页面
   - 创建了`HeroSection.vue`组件示例
   - 创建了`DigitalHumanSection.vue`组件示例

### 使用的Tailwind特性

1. **响应式设计**
   - 使用`lg:`、`md:`等前缀实现不同屏幕尺寸的样式
   - 使用`grid`和`flex`布局系统

2. **深色模式**
   - 配置了深色模式支持

3. **自定义动画**
   - 定义了滚动、浮动等动画效果
   - 使用`animate-`类应用动画

4. **组件提取**
   - 使用`@apply`将常用样式组合提取为可复用类

## 项目结构

### 新的项目结构

```
xiaoa-nuxt/
├── tailwind.config.js     # Tailwind配置文件
├── src/
│   ├── assets/
│   │   ├── css/
│   │   │   └── main.css   # Tailwind入口文件
│   ├── components/
│   │   ├── global/        # 全局组件
│   │   ├── home/          # 首页相关组件
│   │   └── ui/            # UI组件
│   ├── layouts/
│   │   └── default.vue    # 默认布局
│   └── pages/
│       └── index.vue      # 首页
└── nuxt.config.ts         # Nuxt配置文件
```

### 原始项目结构（已更新）

```
xiaoa-nuxt/
├── app/ (Nuxt 3 app目录) ✅
│   └── app.vue (应用主入口) ✅
├── assets/ (静态资源) ✅
│   ├── css/ (Tailwind CSS文件) ✅
│   │   └── main.css (Tailwind入口文件) ✅
│   └── images/ (图片资源) ✅
├── components/ (Vue组件) ✅
│   ├── global/ (全局通用组件) ✅
│   │   ├── TheHeader.vue (头部导航) ✅
│   │   ├── TheFooter.vue (页脚) ✅
│   │   └── LoadingAnimation.vue (加载动画) ✅
│   ├── ui/ (界面组件) ✅
│   │   ├── AppButton.vue (按钮组件) ✅
│   │   ├── AppCard.vue (卡片组件) ✅
│   │   └── FeatureCard.vue (特性卡片组件) ✅
│   └── home/ (首页组件) ✅
│       ├── HeroSection.vue (英雄区域) ✅
│       ├── DigitalHumanSection.vue (数字人部分) ✅
│       ├── DistributionSection.vue (分发系统) ✅
│       ├── AiAgentSection.vue (智能体部分) ✅
│       ├── MiniAppSection.vue (小程序/客户端) ✅
│       ├── AboutCompanySection.vue (关于公司) ✅
│       └── ContactSection.vue (联系我们) ✅
├── layouts/ (布局模板) ✅
│   └── default.vue (默认布局) ✅
├── pages/ (页面文件) ✅
│   └── index.vue (首页) ✅
├── plugins/ (插件) ✅
│   └── anime.js (Anime.js动画插件) ✅
├── public/ (公共资源) ✅
│   ├── favicon.ico ✅
│   └── robots.txt ✅
└── utils/ (工具函数) ✅
    └── animation.js (动画辅助函数) ✅
```

## 项目完成状态

- ✅ 项目基础结构搭建
- ✅ Tailwind CSS 样式系统配置 (更新：从SCSS迁移到Tailwind)
- ✅ 组件化架构设计
- ✅ 基础 UI 组件开发（按钮、卡片等）
- ✅ 全局组件开发（头部、页脚、加载动画）
- ✅ 首页部分组件实现：
  - ✅ 英雄区域
  - ✅ 数字人部分
  - ✅ 分发系统
  - ✅ 智能体部分
  - ✅ 小程序/客户端
  - ✅ 关于公司
  - ✅ 联系我们
- ✅ 页面 SEO 优化
- ✅ 动画系统实现
  - ✅ 基于 Tailwind 的动画系统
  - ✅ 动画辅助函数工具实现
- ⬜ 响应式布局优化
- ⬜ 性能优化

## 功能特性

- 响应式设计，适配各种设备
- 优化 SEO，使用 Nuxt.js 的内置 SEO 功能
- 丰富的动画效果，提升用户体验
- Tailwind CSS 样式系统，提高开发效率
- 组件化架构，提高代码复用性
- 动画系统模块化，易于维护和扩展

## 已完成的其他工作

1. 将原 HTML 模板转换为 Nuxt.js 组件化结构
2. 实现了页面基本骨架和布局
3. 实现了所有主要区域的组件和样式
4. 增强了 SEO 配置
5. 实现了全局 UI 组件系统

## 待办事项

### 1. 完成Tailwind CSS迁移
- [ ] 完成其他部分组件的重构
- [ ] 优化动画和交互效果
- [ ] 进一步提取常用组件
- [ ] 优化移动端体验

### 2. 响应式布局优化
- [ ] 优化移动端导航菜单交互体验
- [ ] 调整各组件在小屏设备上的布局和间距
- [ ] 实现自适应图片加载策略
- [ ] 处理极小尺寸屏幕的边缘情况

### 3. 数据交互实现
- [ ] 实现联系表单的数据验证和提交功能
- [ ] 创建后台 API 连接模块
- [ ] 添加数据加载状态和错误处理
- [ ] 实现数据缓存策略

### 4. 性能优化
- [ ] 实现组件和路由的懒加载
- [ ] 优化首屏加载速度
- [ ] 添加资源预加载策略
- [ ] 优化图片资源，使用 WebP 格式
- [ ] 实现 CSS 和 JS 的代码分割
- [ ] 添加 Service Worker 支持离线访问

### 5. 新功能开发
- [ ] 添加多语言支持
- [ ] 实现暗色模式
- [ ] 添加页面过渡动画
- [ ] 实现案例展示模块
- [ ] 开发新闻/博客部分

### 6. 测试与部署
- [ ] 添加单元测试
- [ ] 实现 E2E 测试
- [ ] 配置 CI/CD 流程
- [ ] 优化构建流程
- [ ] 部署到生产环境
