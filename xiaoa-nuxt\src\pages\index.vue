<template>
  <div>
    <!-- 首页主要内容区域 -->
    <!-- 首屏英雄区域，展示品牌主要价值主张 -->
    <HeroSection />

    <!-- 数字人技术展示区域，介绍AI 4.0超高清克隆技术 -->
    <DigitalHumanSection />

    <!-- 智能手机引流方案展示区域，介绍手机端功能 -->
    <SmartPhoneSection />

    <!-- 分发系统展示区域，介绍内容分发能力 -->
    <DistributionSection />

    <!-- AI智能体展示区域，介绍智能交互能力 -->
    <AIAgentSection />

    <!-- 小程序和客户端解决方案展示区域 -->
    <MiniAppSection />

    <!-- 关于公司介绍区域 -->
    <AboutCompanySection />

    <!-- 联系信息和表单区域 -->
    <ContactSection />

    <!-- 网站底部区域 -->
    <TheFooter />
  </div>
</template>

<script setup lang="ts">
// 导入所有页面组件
import HeroSection from '~/components/home/<USER>';
import DigitalHumanSection from '~/components/home/<USER>';
import SmartPhoneSection from '~/components/home/<USER>';
import DistributionSection from '~/components/home/<USER>';
import AIAgentSection from '~/components/home/<USER>';
import MiniAppSection from '~/components/home/<USER>';
import AboutCompanySection from '~/components/home/<USER>';
import ContactSection from '~/components/home/<USER>';
import TheFooter from '~/components/global/TheFooter.vue';

// SEO优化设置
useHead({
  title: '小A引擎 - 数字人智能体系统',
  meta: [
    // 网站描述，重要的SEO元素，简明描述网站功能与服务
    {
      name: 'description',
      content: '小A引擎是引领未来交互方式的智能数字人系统，提供数字人、分发系统、智能体和全平台客户端解决方案。'
    },
    // 关键词，帮助搜索引擎索引
    {
      name: 'keywords',
      content: '小A引擎,数字人,智能体,分发系统,客户端,AI,虚拟数字人'
    },
    // Open Graph协议标签，优化社交媒体分享效果
    {
      property: 'og:title',
      content: '小A引擎 - 数字人智能体系统'
    },
    {
      property: 'og:description',
      content: '小A引擎是引领未来交互方式的智能数字人系统，提供数字人、分发系统、智能体和全平台客户端解决方案。'
    },
    {
      property: 'og:url',
      content: 'https://xiaoa.example.com'
    },
    {
      property: 'og:image',
      content: '/og-image.jpg'
    }
  ],
  // 规范链接，避免重复内容问题
  link: [
    {
      rel: 'canonical',
      href: 'https://xiaoa.example.com'
    }
  ]
});
</script>