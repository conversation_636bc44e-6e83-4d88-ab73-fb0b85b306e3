<template>
  <section id="mini-app" class="relative section py-16 overflow-hidden">
    <div class="container mx-auto px-4">
      <h2 class="section-title text-center text-4xl font-bold mb-8 fade-in-down">社区团购<span
          class="text-primary">小A甄选</span></h2>

      <div class="flex justify-center mb-12">
        <p class="mini-app-intro text-lg fade-in-up" style="--delay: 0.2s">
          “社区团购小A甄选”小程序，专为社区团购场景打造，助力商家实现用户资产沉淀、精准运营与高效转化。平台支持多种团购玩法，帮助商家低成本获取私域流量，提升用户粘性与复购率，打造从拉新、转化到长期留存的全链路数字化团购解决方案。
        </p>
      </div>

      <div class="flex flex-col lg:flex-row items-center gap-10">
        <!-- 新增图片区域 -->
        <div class="w-full lg:w-1/2 flex justify-center mb-8 lg:mb-0">
          <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/77928dba8ec53dbc96d0b7831d68831.png" alt="小A甄选"
            class="rounded-2xl shadow-lg object-cover"
            style="aspect-ratio:9/16; max-width:320px; width:100%; height:auto;" />
        </div>
        <div class="w-full lg:w-1/2">
          <div class="platform-tabs bg-white rounded-xl shadow-lg p-6">
            <div class="platform-nav flex mb-6 border-b">
              <button class="nav-link px-4 py-2 mx-1 slide-in-down active" style="--delay: 0.9s">
                <i class="fas fa-code mr-2"></i> 小A甄选
              </button>
            </div>

            <div class="platform-content fade-in-up" style="--delay: 1s">
              <div class="tab-pane block">
                <div class="platform-card">
                  <h3 class="text-xl font-bold mb-4 text-primary">小A甄选核心价值</h3>
                  <ul class="platform-features space-y-5">
                    <li class="flex items-start">
                      <i class="fas fa-user-friends text-primary mt-1 mr-3"></i>
                      <span class="text-dark">用户资产沉淀与精准运营：从“流量收割”到“用户深耕”</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-recycle text-primary mt-1 mr-3"></i>
                      <span class="text-dark">低成本高转化的流量复用：私域流量的“滚雪球效应”</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-hand-holding-heart text-primary mt-1 mr-3"></i>
                      <span class="text-dark">用户粘性提升与生命周期延长：从“一次性交易”到“长期关系”</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16">
        <div class="benefit-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all fade-in-up"
          style="--delay: 1.2s">
          <div
            class="benefit-icon bg-gradient-to-r from-primary to-accent w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
            <i class="fas fa-bolt text-2xl text-white"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-center text-primary">高效获客</h3>
          <p class="text-dark text-center">通过全渠道覆盖，精准触达目标用户群体，提升转化率和用户留存。</p>
        </div>
        <div class="benefit-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all fade-in-up"
          style="--delay: 1.35s">
          <div
            class="benefit-icon bg-gradient-to-r from-primary to-accent w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
            <i class="fas fa-chart-line text-2xl text-white"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-center text-primary">数据洞察</h3>
          <p class="text-dark text-center">全面的数据分析系统，帮助您了解用户行为，优化产品体验和营销策略。</p>
        </div>
        <div class="benefit-card bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all fade-in-up"
          style="--delay: 1.5s">
          <div
            class="benefit-icon bg-gradient-to-r from-primary to-accent w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
            <i class="fas fa-paint-brush text-2xl text-white"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-center text-primary">品牌塑造</h3>
          <p class="text-dark text-center">统一的品牌形象和个性化体验，增强品牌识别度和用户忠诚度。</p>
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="mini-app-bg absolute inset-0 overflow-hidden pointer-events-none">
      <div class="bg-shape bg-shape-1 absolute rounded-full bg-primary opacity-10 scale-in"></div>
      <div class="bg-shape bg-shape-2 absolute rounded-full bg-accent opacity-10 scale-in" style="--delay: 0.2s"></div>
      <div class="bg-shape bg-shape-3 absolute rounded-full bg-primary opacity-10 scale-in" style="--delay: 0.4s"></div>
      <div class="bg-shape bg-shape-4 absolute rounded-full bg-accent opacity-10 scale-in" style="--delay: 0.6s"></div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 不再需要activeTab
</script>

<style scoped>
/* CSS变量 */
:root {
  --color-primary-rgb: 59, 130, 246;
  /* 根据实际主色调调整RGB值 */
  --color-accent-rgb: 255, 107, 107;
  /* 强调色RGB值 */
}

/* 设备样式 */

/* 标签页样式 */
.nav-link {
  position: relative;
  font-weight: 500;
  transition: all 0.3s ease;
  color: #666;
  border-bottom: 2px solid transparent;
}

.nav-link:hover {
  color: var(--color-primary);
}

.nav-link.active {
  color: var(--color-accent, #FF6B6B);
  font-weight: 700;
  border-bottom: 3px solid var(--color-accent, #FF6B6B);
  background-color: rgba(var(--color-accent-rgb, 255, 107, 107), 0.1);
  border-radius: 6px 6px 0 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

/* 优势卡片悬停效果 */
.benefit-card {
  transition: all 0.3s ease;
  border-top: 4px solid transparent;
}

.benefit-card:hover {
  transform: translateY(-5px);
  border-top: 4px solid var(--color-primary);
}

/* 背景形状 */
.bg-shape {
  transform: scale(0);
}

.bg-shape-1 {
  width: 400px;
  height: 400px;
  top: 10%;
  left: -100px;
}

.bg-shape-2 {
  width: 300px;
  height: 300px;
  top: 60%;
  right: -50px;
}

.bg-shape-3 {
  width: 200px;
  height: 200px;
  bottom: 10%;
  left: 15%;
}

.bg-shape-4 {
  width: 250px;
  height: 250px;
  top: 20%;
  right: 20%;
}

/* 动画定义 */
.fade-in-down {
  opacity: 0;
  transform: translateY(-30px);
  animation: fadeInDown 1s ease-out forwards;
}

.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease-out forwards;
  animation-delay: var(--delay, 0s);
}

.slide-in-down {
  opacity: 0;
  transform: translateY(-20px);
  animation: slideInDown 0.8s ease-out forwards;
  animation-delay: var(--delay, 0s);
}

.scale-in {
  animation: scaleIn 1s ease-out forwards;
  animation-delay: var(--delay, 0s);
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 0.1;
  }
}
</style>