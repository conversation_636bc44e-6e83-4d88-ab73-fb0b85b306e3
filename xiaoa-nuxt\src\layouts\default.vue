<template>
  <div class="flex flex-col min-h-screen">
    <LoadingAnimation />
    <TheHeader />
    <main class="flex-grow">
      <slot />
    </main>
  </div>
</template>

<script setup lang="ts">
import { useHead } from '#imports';
import LoadingAnimation from '~/components/global/LoadingAnimation.vue';
import TheHeader from '~/components/global/TheHeader.vue';

// 全局 SEO 设置
useHead({
  titleTemplate: '%s - 小A引擎',
  meta: [
    {
      name: 'viewport',
      content: 'width=device-width, initial-scale=1'
    },
    {
      charset: 'utf-8'
    },
    {
      name: 'description',
      content: '小A引擎是引领未来交互方式的智能数字人系统，提供数字人、分发系统、智能体和全平台客户端解决方案。'
    },
    {
      name: 'format-detection',
      content: 'telephone=no'
    }
  ],
  link: [
    {
      rel: 'icon',
      type: 'image/x-icon',
      href: '/favicon.ico'
    }
  ]
});
</script> 