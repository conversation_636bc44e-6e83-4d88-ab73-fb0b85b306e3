<template>
  <div class="feature-card">
    <div v-if="icon" class="feature-card-icon">
      <i :class="icon"></i>
    </div>
    <h3 class="feature-card-title">{{ title }}</h3>
    <p><slot /></p>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
.feature-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
  transition: all $transition-slow;
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 100%;
  
  &:hover {
    transform: translateY(-10px);
    box-shadow: $shadow-lg;
    border: 1px solid $secondary-color;
  }
  
  &-icon {
    font-size: 2.5rem;
    margin-bottom: $spacing-md;
    color: $secondary-color;
  }
  
  &-title {
    font-size: $font-size-lg;
    font-weight: 600;
    margin-bottom: $spacing-sm;
  }
  
  p {
    color: rgba($light-color, 0.8);
  }
}
</style> 