<template>
  <header>
    <nav class="navbar" :class="{ 'navbar-collapsed': navbarState === 'collapsed' }">
      <div class="container mx-auto px-4 flex items-center justify-between">
        <nuxt-link class="navbar-brand font-bold text-secondary flex items-center" to="/">
          <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250717111115.png"
            alt="数字人科技" class="h-10">
          <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/ed71b025db069bfdff54044d153d992.png"
            alt="数字人标识" class="h-10 ml-2">
        </nuxt-link>
        
        <button 
          class="lg:hidden bg-transparent border-none w-10 h-10 relative cursor-pointer" 
          type="button" 
          @click="toggleNavbar"
        >
          <span class="block w-6 h-0.5 bg-light mx-auto relative before:content-[''] before:w-6 before:h-0.5 before:bg-light before:absolute before:left-0 before:-top-2 before:transition-all before:duration-300 after:content-[''] after:w-6 after:h-0.5 after:bg-light after:absolute after:left-0 after:-bottom-2 after:transition-all after:duration-300"></span>
        </button>
        
        <div 
          class="navbar-collapse transition-all duration-500"
          :class="[
            isNavbarExpanded ? 'block animate-fadeInDown' : 'hidden',
            'lg:flex lg:items-center lg:justify-end lg:flex-grow-1 lg:static'
          ]"
        >
          <ul class="lg:flex lg:items-center p-0 m-0 list-none lg:flex-row flex-col items-center">
            <li class="nav-item lg:mx-2.5 w-full lg:w-auto text-center lg:text-left my-1.5 lg:my-0">
              <nuxt-link class="nav-link block lg:inline-block py-4 lg:py-2.5 px-5 lg:px-2.5" to="/#hero" @click="closeNavbar">首页</nuxt-link>
            </li>
            <li class="nav-item lg:mx-2.5 w-full lg:w-auto text-center lg:text-left my-1.5 lg:my-0">
              <nuxt-link class="nav-link block lg:inline-block py-4 lg:py-2.5 px-5 lg:px-2.5" to="/#digital-human" @click="closeNavbar">数字人</nuxt-link>
            </li>
            <li class="nav-item lg:mx-2.5 w-full lg:w-auto text-center lg:text-left my-1.5 lg:my-0">
              <nuxt-link class="nav-link block lg:inline-block py-4 lg:py-2.5 px-5 lg:px-2.5" to="/#smart-phone" @click="closeNavbar">智能手机</nuxt-link>
            </li>
            <li class="nav-item lg:mx-2.5 w-full lg:w-auto text-center lg:text-left my-1.5 lg:my-0">
              <nuxt-link class="nav-link block lg:inline-block py-4 lg:py-2.5 px-5 lg:px-2.5" to="/#distribution" @click="closeNavbar">分发系统</nuxt-link>
            </li>
            <li class="nav-item lg:mx-2.5 w-full lg:w-auto text-center lg:text-left my-1.5 lg:my-0">
              <nuxt-link class="nav-link block lg:inline-block py-4 lg:py-2.5 px-5 lg:px-2.5" to="/#ai-agent" @click="closeNavbar">智能体</nuxt-link>
            </li>
            <li class="nav-item lg:mx-2.5 w-full lg:w-auto text-center lg:text-left my-1.5 lg:my-0">
              <nuxt-link class="nav-link block lg:inline-block py-4 lg:py-2.5 px-5 lg:px-2.5" to="/#mini-app" @click="closeNavbar">小A甄选</nuxt-link>
            </li>
            <li class="nav-item lg:mx-2.5 w-full lg:w-auto text-center lg:text-left my-1.5 lg:my-0">
              <nuxt-link class="nav-link block lg:inline-block py-4 lg:py-2.5 px-5 lg:px-2.5" to="/#about-company" @click="closeNavbar">关于公司</nuxt-link>
            </li>
            <li class="nav-item lg:mx-2.5 w-full lg:w-auto text-center lg:text-left my-1.5 lg:my-0">
              <nuxt-link class="nav-link block lg:inline-block py-4 lg:py-2.5 px-5 lg:px-2.5" to="/#contact" @click="closeNavbar">联系我们</nuxt-link>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

// 导航栏展开状态
const isNavbarExpanded = ref(false);

// 切换导航栏展开/收起
const toggleNavbar = () => {
  isNavbarExpanded.value = !isNavbarExpanded.value;
};

// 关闭导航栏
const closeNavbar = () => {
  isNavbarExpanded.value = false;
};

// 导航栏滚动效果
const navbarState = ref('expanded'); // 'expanded' 或 'collapsed'
let lastScrollPosition = 0;
let ticking = false;

const updateNavbar = () => {
  const navbar = document.querySelector('.navbar');
  if (!navbar) return;
  
  const currentScrollPos = window.scrollY;

  if (currentScrollPos > 50 && navbarState.value === 'expanded') {
    navbarState.value = 'collapsed';
  } else if (currentScrollPos <= 50 && navbarState.value === 'collapsed') {
    navbarState.value = 'expanded';
  }

  lastScrollPosition = currentScrollPos;
  ticking = false;
};

const handleScroll = () => {
  if (!ticking) {
    window.requestAnimationFrame(() => {
      updateNavbar();
    });
    ticking = true;
  }
};

// 监听滚动事件
onMounted(() => {
  if (process.client) {
    window.addEventListener('scroll', handleScroll);
    updateNavbar(); // 初始化导航栏状态
  }
});

// 移除滚动监听
onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('scroll', handleScroll);
  }
});
</script>

<style lang="postcss">
.navbar {
  @apply fixed w-full z-50 top-0 left-1/2 -translate-x-1/2 py-5 bg-dark/30 backdrop-blur-lg transition-all duration-300;
}

.navbar.navbar-collapsed {
  @apply w-[90%] top-[15px] rounded-xl bg-dark/60 border border-white/10 shadow-nav py-2.5;
}

/* 移动端折叠菜单样式 */
@media (max-width: 1023px) {
  .navbar-collapse {
    @apply fixed top-20 left-0 right-0 bg-dark/95 backdrop-blur-lg py-5 rounded-b-xl shadow-lg;
  }
}

/* 导航链接样式 */
.nav-link {
  @apply text-light relative overflow-hidden transition-colors hover:text-secondary font-medium;
}

.nav-link::after {
  content: '';
  @apply absolute bottom-0 left-0 w-0 h-0.5 bg-accent transition-all duration-300;
}

.nav-link:hover::after {
  @apply w-full;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInDown {
  animation: fadeInDown 0.5s forwards;
}
</style> 