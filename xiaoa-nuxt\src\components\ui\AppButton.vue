<template>
  <button 
    :class="[
      'app-btn',
      variant ? `app-btn-${variant}` : 'app-btn-primary',
      size ? `app-btn-${size}` : '',
      { 'app-btn-block': block }
    ]"
    :type="type"
    :disabled="disabled"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
defineProps({
  variant: {
    type: String,
    default: 'primary',
    validator: (value: string) => ['primary', 'secondary', 'outline', 'text'].includes(value)
  },
  size: {
    type: String,
    default: '',
    validator: (value: string) => ['sm', 'lg', ''].includes(value)
  },
  block: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'button'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
</script>

<style lang="scss" scoped>
.app-btn {
  display: inline-block;
  padding: $spacing-sm $spacing-lg;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all $transition-base;
  border: none;
  outline: none;
  text-align: center;
  font-size: $font-size-base;
  
  &-primary {
    @include gradient(45deg, $primary-color, $accent-color);
    color: $light-color;
    
    &:hover {
      transform: translateY(-3px);
      box-shadow: $shadow-lg;
    }
  }
  
  &-secondary {
    background-color: $secondary-color;
    color: $dark-color;
    
    &:hover {
      transform: translateY(-3px);
      box-shadow: $shadow-md;
      background-color: lighten($secondary-color, 5%);
    }
  }
  
  &-outline {
    background-color: transparent;
    border: 2px solid $secondary-color;
    color: $secondary-color;
    
    &:hover {
      background-color: rgba($secondary-color, 0.1);
      transform: translateY(-3px);
    }
  }
  
  &-text {
    background-color: transparent;
    color: $secondary-color;
    padding-left: $spacing-xs;
    padding-right: $spacing-xs;
    
    &:hover {
      color: $accent-color;
      text-decoration: underline;
    }
  }
  
  &-sm {
    padding: $spacing-xs $spacing-md;
    font-size: $font-size-sm;
  }
  
  &-lg {
    padding: $spacing-md $spacing-xl;
    font-size: $font-size-lg;
  }
  
  &-block {
    display: block;
    width: 100%;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}
</style> 