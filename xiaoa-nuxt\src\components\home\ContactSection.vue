<template>
  <section id="contact" class="section">
    <div class="container">
      <h2 class="section-title">联系<span>我们</span></h2>

      <div class="row mb-5">
        <div class="col-lg-8 mx-auto text-center">
          <p class="text-lg opacity-90 mb-12 contact-intro">想要了解更多关于我们的数字人智能体系统？请随时与我们联系，我们将为您提供专业的咨询服务。</p>
        </div>
      </div>

      <div class="row">
        <div class="col-lg-6">
          <div class="flex flex-col gap-8 mb-12">
            <div class="flex items-center bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 contact-card">
              <div class="text-4xl text-secondary mr-5 flex-shrink-0">
                <i class="fas fa-map-marker-alt"></i>
              </div>
              <div>
                <h3 class="text-xl font-semibold mb-1">公司总部地址</h3>
                <p class="opacity-80">湖南省长沙市天心区芙蓉南路二段249号中建芙蓉工社1栋10层 小A AI引擎</p>
              </div>
            </div>

            <div class="flex items-center bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 contact-card">
              <div class="text-4xl text-secondary mr-5 flex-shrink-0">
                <i class="fas fa-phone"></i>
              </div>
              <div>
                <h3 class="text-xl font-semibold mb-1">联系电话</h3>
                <p class="opacity-80">＋86 17788959398</p>
              </div>
            </div>

            <div class="flex items-center bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 contact-card">
              <div class="text-4xl text-secondary mr-5 flex-shrink-0">
                <i class="fas fa-envelope"></i>
              </div>
              <div>
                <h3 class="text-xl font-semibold mb-1">电子邮箱</h3>
                <p class="opacity-80"><EMAIL></p>
              </div>
            </div>

            <div class="flex items-center bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 contact-card">
              <div class="text-4xl text-secondary mr-5 flex-shrink-0">
                <i class="fas fa-clock"></i>
              </div>
              <div>
                <h3 class="text-xl font-semibold mb-1">工作时间</h3>
                <p class="opacity-80">周一至周六: 9:00 - 18:00</p>
              </div>
            </div>
          </div>

          <div class="flex gap-5 justify-center">
            <a href="#" class="text-2xl text-secondary hover:text-accent hover:scale-110 transition-all duration-300 social-icon">
              <i class="fab fa-weixin"></i>
            </a>
            <a href="#" class="text-2xl text-secondary hover:text-accent hover:scale-110 transition-all duration-300 social-icon">
              <i class="fab fa-weibo"></i>
            </a>
            <a href="#" class="text-2xl text-secondary hover:text-accent hover:scale-110 transition-all duration-300 social-icon">
              <i class="fab fa-linkedin"></i>
            </a>
            <a href="#" class="text-2xl text-secondary hover:text-accent hover:scale-110 transition-all duration-300 social-icon">
              <i class="fab fa-twitter"></i>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden z-[-1]">
      <div class="absolute rounded-full bg-secondary/5 opacity-50 w-[400px] h-[400px] top-[-100px] left-[-100px] contact-shape"></div>
      <div class="absolute rounded-full bg-secondary/5 opacity-50 w-[500px] h-[500px] bottom-[100px] right-[-200px] contact-shape">
      </div>
      <div class="absolute rounded-full bg-secondary/5 opacity-50 w-[300px] h-[300px] bottom-[-150px] right-[200px] contact-shape">
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 表单数据
const formData = ref({
  name: '',
  email: '',
  phone: '',
  interest: '',
  message: ''
});

// 表单提交处理
const handleSubmit = () => {
  // 在实际项目中，这里会发送表单数据到后端API
  console.log('表单提交数据:', formData.value);
  // 提交后可以重置表单
  // formData.value = { name: '', email: '', phone: '', interest: '', message: '' };
  // 并显示提交成功的消息
  alert('感谢您的咨询，我们将尽快与您联系！');
};

// 使用CSS动画替代JavaScript动画
</script>

<style scoped>
/* 为每个元素定义动画 */
.section-title {
  animation: fadeInDown 1s ease-out forwards;
}

.contact-intro {
  animation: fadeInUp 1s ease-out 0.2s forwards;
  opacity: 0;
}

.contact-card {
  animation: fadeInLeft 0.8s ease-out forwards;
  opacity: 0;
}

.contact-card:nth-child(1) {
  animation-delay: 0.15s;
}

.contact-card:nth-child(2) {
  animation-delay: 0.3s;
}

.contact-card:nth-child(3) {
  animation-delay: 0.45s;
}

.contact-card:nth-child(4) {
  animation-delay: 0.6s;
}

.social-icon {
  animation: scaleIn 0.6s ease-out forwards;
  opacity: 0;
}

.social-icon:nth-child(1) {
  animation-delay: 0.8s;
}

.social-icon:nth-child(2) {
  animation-delay: 0.9s;
}

.social-icon:nth-child(3) {
  animation-delay: 1s;
}

.social-icon:nth-child(4) {
  animation-delay: 1.1s;
}

.contact-shape {
  animation: scaleIn 1s ease-out forwards;
  opacity: 0;
}

.contact-shape:nth-child(1) {
  animation-delay: 0.2s;
}

.contact-shape:nth-child(2) {
  animation-delay: 0.4s;
}

.contact-shape:nth-child(3) {
  animation-delay: 0.6s;
}

/* 动画关键帧 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>