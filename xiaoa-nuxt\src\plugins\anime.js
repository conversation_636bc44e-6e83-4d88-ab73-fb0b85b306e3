// anime.js 插件封装
import { defineNuxtPlugin } from '#app'

export default defineNuxtPlugin(async (nuxtApp) => {
  // 仅在客户端导入anime.js
  if (process.client) {
    const animeModule = await import('animejs')
    const anime = animeModule.default
    
    // 通用动画方法
    const animations = {
      // 淡入上移动画
      fadeInUp: (targets, options = {}) => {
        return anime({
          targets,
          translateY: [50, 0],
          opacity: [0, 1],
          duration: options.duration || 1000,
          delay: options.delay || 0,
          easing: options.easing || 'easeOutQuad',
          ...options
        })
      },
      
      // 淡入下移动画
      fadeInDown: (targets, options = {}) => {
        return anime({
          targets,
          translateY: [-50, 0],
          opacity: [0, 1],
          duration: options.duration || 1000,
          delay: options.delay || 0,
          easing: options.easing || 'easeOutQuad',
          ...options
        })
      },
      
      // 淡入左移动画
      fadeInLeft: (targets, options = {}) => {
        return anime({
          targets,
          translateX: [-50, 0],
          opacity: [0, 1],
          duration: options.duration || 1000,
          delay: options.delay || 0,
          easing: options.easing || 'easeOutQuad',
          ...options
        })
      },
      
      // 淡入右移动画
      fadeInRight: (targets, options = {}) => {
        return anime({
          targets,
          translateX: [50, 0],
          opacity: [0, 1],
          duration: options.duration || 1000,
          delay: options.delay || 0,
          easing: options.easing || 'easeOutQuad',
          ...options
        })
      },
      
      // 缩放动画
      scale: (targets, options = {}) => {
        return anime({
          targets,
          scale: options.scaleValues || [0, 1],
          opacity: options.opacityValues || [0, 1],
          duration: options.duration || 800,
          delay: options.delay || 0,
          easing: options.easing || 'easeOutElastic(1, .5)',
          ...options
        })
      },
      
      // 随机动画
      random: (targets, options = {}) => {
        const randomValues = (property, min, max) => {
          return anime.random(min, max);
        };
        
        return anime({
          targets,
          translateX: () => randomValues('translateX', options.minX || -20, options.maxX || 20),
          translateY: () => randomValues('translateY', options.minY || -20, options.maxY || 20),
          scale: () => randomValues('scale', options.minScale || 0.9, options.maxScale || 1.1),
          duration: options.duration || 5000,
          easing: options.easing || 'easeInOutQuad',
          complete: options.loop ? function() { animations.random(targets, options); } : null,
          ...options
        });
      },
      
      // 交错动画
      stagger: (targets, options = {}) => {
        return anime({
          targets,
          translateY: options.translateYValues || [50, 0],
          opacity: options.opacityValues || [0, 1],
          duration: options.duration || 800,
          delay: anime.stagger(options.staggerValue || 100, options.staggerOptions || {}),
          easing: options.easing || 'easeOutQuad',
          ...options
        })
      },
    }

    // 提供anime.js实例和封装的动画方法
    nuxtApp.provide('anime', {
      anime,
      animations
    })
  }
}) 