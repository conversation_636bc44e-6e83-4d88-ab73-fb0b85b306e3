<template>
  <section id="about-company" class="section bg-gradient-primary relative overflow-hidden">
    <div class="container mx-auto px-4">
      <AnimatedContent :distance="50" direction="vertical" :reverse="false" :duration="0.8" ease="power3.out"
        :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0" class="h-full">
        <h2 class="section-title transform translate-y-0 opacity-100 transition-all duration-1000">
          关于<span>公司</span>
        </h2>
      </AnimatedContent>

      <!-- 公司介绍 -->
      <div class="mb-16">
        <FadeContent :blur="true" :duration="1000" :delay="200" :threshold="0.1" :initial-opacity="0" easing="ease-out"
          class-name="h-full">
          <h3 class="text-2xl md:text-3xl font-bold mb-8 text-center">公司介绍</h3>
        </FadeContent>
        <div class="max-w-4xl mx-auto">
          <FadeContent :blur="true" :duration="1000" :delay="400" :threshold="0.1" :initial-opacity="0"
            easing="ease-out" class-name="h-full">
            <div class="bg-white/5 backdrop-blur-md rounded-xl p-8 border border-white/10">
              <p class="text-lg leading-relaxed text-center">
                长沙小艾引擎科技有限公司正式成立于2025年，作为深耕AI算力基建与场景创新的科技企业，自2017年起前瞻布局智能算力基础设施研发，以“<span
                  class="text-secondary font-semibold">算力赋能千行百业</span>”为使命，构建了覆盖“<span
                  class="text-secondary font-semibold">底层架构-核心算法-行业应用</span>”的全链路技术体系。
              </p>
            </div>
          </FadeContent>
        </div>
      </div>

      <!-- 核心优势 -->
      <div class="mb-16">
        <FadeContent :blur="true" :duration="1000" :delay="600" :threshold="0.1" :initial-opacity="0" easing="ease-out"
          class-name="h-full">
          <h3 class="text-2xl md:text-3xl font-bold mb-8 text-center">核心优势</h3>
        </FadeContent>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AnimatedContent :distance="50" direction="vertical" :reverse="false" :duration="0.8" ease="power3.out"
            :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0" class="h-full">
            <div
              class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 hover:border-secondary transition-all duration-300">
              <div class="text-4xl text-secondary mb-4">
                <i class="fas fa-server"></i>
              </div>
              <h4 class="text-xl font-semibold mb-3">算力领跑</h4>
              <p>长三角地区首个民营万卡级AI算力中心，支持千亿参数模型训练</p>
            </div>
          </AnimatedContent>

          <AnimatedContent :distance="50" direction="vertical" :reverse="false" :duration="0.8" ease="power3.out"
            :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0.2" class="h-full">
            <div
              class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 hover:border-secondary transition-all duration-300">
              <div class="text-4xl text-secondary mb-4">
                <i class="fas fa-lightbulb"></i>
              </div>
              <h4 class="text-xl font-semibold mb-3">自主创新</h4>
              <p>拥有120+项AI相关专利，算法库覆盖NLP、CV、多模态交互全领域</p>
            </div>
          </AnimatedContent>

          <AnimatedContent :distance="50" direction="vertical" :reverse="false" :duration="0.8" ease="power3.out"
            :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0.4" class="h-full">
            <div
              class="bg-white/5 backdrop-blur-md h-full rounded-xl p-6 border border-white/10 hover:border-secondary transition-all duration-300">
              <div class="text-4xl text-secondary mb-4">
                <i class="fas fa-link"></i>
              </div>
              <h4 class="text-xl font-semibold mb-3">场景闭环</h4>
              <p>从底层算力到应用层产品，提供端到端百业全链路解决方案</p>
            </div>
          </AnimatedContent>
        </div>
      </div>

      <!-- 技术实力 -->
      <div class="mb-16">
        <FadeContent :blur="true" :duration="1000" :delay="800" :threshold="0.1" :initial-opacity="0" easing="ease-out"
          class-name="h-full">
          <h3 class="text-2xl md:text-3xl font-bold mb-8 text-center">技术实力</h3>
        </FadeContent>
        <FadeContent :blur="true" :duration="1000" :delay="1000" :threshold="0.1" :initial-opacity="0" easing="ease-out"
          class-name="h-full">
          <div class="bg-white/5 backdrop-blur-md rounded-xl p-8 border border-white/10">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h4 class="text-xl font-semibold mb-4 text-secondary">自研技术框架</h4>
                <ul class="space-y-3">
                  <li class="flex items-start">
                    <i class="fas fa-check text-secondary mt-1 mr-3"></i>
                    <span>依托自研的"天枢"AI框架与动态图混合编程技术</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check text-secondary mt-1 mr-3"></i>
                    <span>训练效率较传统框架提升40%</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check text-secondary mt-1 mr-3"></i>
                    <span>多模态大模型压缩技术，推理成本压缩至每百万token 0.27美元</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 class="text-xl font-semibold mb-4 text-secondary">核心产品线</h4>
                <ul class="space-y-3">
                  <li class="flex items-start">
                    <i class="fas fa-check text-secondary mt-1 mr-3"></i>
                    <span>智能内容生产</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check text-secondary mt-1 mr-3"></i>
                    <span>工业质检</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check text-secondary mt-1 mr-3"></i>
                    <span>数字人克隆三大核心产品线</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </FadeContent>
      </div>

      <!-- 发展历程 -->
      <div class="mb-16">
        <FadeContent :blur="true" :duration="1000" :delay="1200" :threshold="0.1" :initial-opacity="0" easing="ease-out"
          class-name="h-full">
          <h3 class="text-2xl md:text-3xl font-bold mb-8 text-center">发展历程</h3>
        </FadeContent>
        <div class="relative">
          <!-- 时间线 -->
          <div class="absolute left-1/2 transform -translate-x-1/2 w-1 bg-secondary/30 h-full"></div>

          <div class="space-y-8">
            <AnimatedContent :distance="50" direction="horizontal" :reverse="false" :duration="0.8" ease="power3.out"
              :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0" class="h-full">
              <div class="flex items-center">
                <div class="w-1/2 pr-8 text-right">
                  <div class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 timeline-card">
                    <h4 class="text-xl font-semibold text-secondary">2017年 | 算力筑基</h4>
                    <p>创始人赖总（赖仁生）携核心技术团队投资7000万，构建GPU超级算力中心，奠定AI研发基础设施优势</p>
                  </div>
                </div>
                <div class="w-4 h-4 bg-secondary rounded-full relative z-10"></div>
                <div class="w-1/2 pl-8"></div>
              </div>
            </AnimatedContent>

            <AnimatedContent :distance="50" direction="horizontal" :reverse="true" :duration="0.8" ease="power3.out"
              :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0.2" class="h-full">
              <div class="flex items-center">
                <div class="w-1/2 pr-8"></div>
                <div class="w-4 h-4 bg-secondary rounded-full relative z-10"></div>
                <div class="w-1/2 pl-8">
                  <div class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 timeline-card">
                    <h4 class="text-xl font-semibold text-secondary">2018年 | 投入研发</h4>
                    <p>初始开发团队成立，闭关深造，研发大模型与架构底层逻辑</p>
                  </div>
                </div>
              </div>
            </AnimatedContent>

            <AnimatedContent :distance="50" direction="horizontal" :reverse="false" :duration="0.8" ease="power3.out"
              :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0.4" class="h-full">
              <div class="flex items-center">
                <div class="w-1/2 pr-8 text-right">
                  <div class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 timeline-card">
                    <h4 class="text-xl font-semibold text-secondary">2020年 | 战略升级</h4>
                    <p>完成AI算法框架与软件生态的底层技术突破，形成"算力+算法+场景"三位一体技术架构，获多项国家级技术专利认证</p>
                  </div>
                </div>
                <div class="w-4 h-4 bg-secondary rounded-full relative z-10"></div>
                <div class="w-1/2 pl-8"></div>
              </div>
            </AnimatedContent>

            <AnimatedContent :distance="50" direction="horizontal" :reverse="true" :duration="0.8" ease="power3.out"
              :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0.6" class="h-full">
              <div class="flex items-center">
                <div class="w-1/2 pr-8"></div>
                <div class="w-4 h-4 bg-secondary rounded-full relative z-10"></div>
                <div class="w-1/2 pl-8">
                  <div class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 timeline-card">
                    <h4 class="text-xl font-semibold text-secondary">2023年 | 产品破局</h4>
                    <p>推出核心产品——小A智能剪辑与「小A数字人」克隆系统，服务大B端领域客户超50家</p>
                  </div>
                </div>
              </div>
            </AnimatedContent>

            <AnimatedContent :distance="50" direction="horizontal" :reverse="false" :duration="0.8" ease="power3.out"
              :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0.8" class="h-full">
              <div class="flex items-center">
                <div class="w-1/2 pr-8 text-right">
                  <div class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 timeline-card">
                    <h4 class="text-xl font-semibold text-secondary">2024年 | 行业深耕</h4>
                    <p>与行业TOP 10企业达成战略合作，累计服务客户超200家，技术解决方案复购率达92%</p>
                  </div>
                </div>
                <div class="w-4 h-4 bg-secondary rounded-full relative z-10"></div>
                <div class="w-1/2 pl-8"></div>
              </div>
            </AnimatedContent>

            <AnimatedContent :distance="50" direction="horizontal" :reverse="true" :duration="0.8" ease="power3.out"
              :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="1.0" class="h-full">
              <div class="flex items-center">
                <div class="w-1/2 pr-8"></div>
                <div class="w-4 h-4 bg-secondary rounded-full relative z-10"></div>
                <div class="w-1/2 pl-8">
                  <div class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10 timeline-card">
                    <h4 class="text-xl font-semibold text-secondary">2025年 | 生态布局</h4>
                    <p>启动"AI+产业"生态计划，构建开发者社区与行业知识库，推动技术普惠化</p>
                  </div>
                </div>
              </div>
            </AnimatedContent>
          </div>
        </div>
      </div>

      <!-- 公司愿景 -->
      <div class="mb-16">
        <FadeContent :blur="true" :duration="1000" :delay="1400" :threshold="0.1" :initial-opacity="0" easing="ease-out"
          class-name="h-full">
          <h3 class="text-2xl md:text-3xl font-bold mb-8 text-center">公司愿景</h3>
        </FadeContent>
        <div class="max-w-4xl mx-auto">
          <FadeContent :blur="true" :duration="1000" :delay="1600" :threshold="0.1" :initial-opacity="0"
            easing="ease-out" class-name="h-full">
            <div class="bg-white/5 backdrop-blur-md rounded-xl p-8 border border-white/10">
              <h4 class="text-xl font-semibold mb-4 text-secondary text-center">
                以智能为翼，赋能千行百业，成为全球AI深度赋能时代的价值引领者与生态共建者
              </h4>
              <p class="text-lg leading-relaxed mb-4">
                我们致力于突破AI技术创新的边界，以更懂场景、更有温度的技术能力，成为全球各行业数字化转型的核心驱动力。
              </p>
              <p class="text-lg leading-relaxed">
                从智能制造的柔性生产线到医疗健康的精准诊疗，从教育公平的个性化学习到城市治理的智慧决策，
                让AI真正成为解决复杂问题的"通用工具"，而非实验室的"技术秀场"。
              </p>
            </div>
          </FadeContent>
        </div>
      </div>

      <!-- 622原则 -->
      <div class="mb-16">
        <FadeContent :blur="true" :duration="1000" :delay="1800" :threshold="0.1" :initial-opacity="0" easing="ease-out"
          class-name="h-full">
          <h3 class="text-2xl md:text-3xl font-bold mb-8 text-center">服务理念</h3>
        </FadeContent>
        <div class="max-w-2xl mx-auto">
          <FadeContent :blur="true" :duration="1000" :delay="2000" :threshold="0.1" :initial-opacity="0"
            easing="ease-out" class-name="h-full">
            <div class="bg-white/5 backdrop-blur-md rounded-xl p-8 border border-white/10 text-center">
              <h4 class="text-2xl font-semibold mb-4 text-secondary">坚持622原则</h4>
              <div class="grid grid-cols-3 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-3xl font-bold text-secondary mb-2">60%</div>
                  <div class="text-sm">服务</div>
                </div>
                <div class="text-center">
                  <div class="text-3xl font-bold text-secondary mb-2">20%</div>
                  <div class="text-sm">方案</div>
                </div>
                <div class="text-center">
                  <div class="text-3xl font-bold text-secondary mb-2">20%</div>
                  <div class="text-sm">技术</div>
                </div>
              </div>
              <p class="text-lg">技术不断迭代，服务创造价值</p>
            </div>
          </FadeContent>
        </div>
      </div>

      <!-- 未来规划 -->
      <div class="mb-16">
        <FadeContent :blur="true" :duration="1000" :delay="2200" :threshold="0.1" :initial-opacity="0" easing="ease-out"
          class-name="h-full">
          <h3 class="text-2xl md:text-3xl font-bold mb-8 text-center">未来规划</h3>
        </FadeContent>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AnimatedContent :distance="50" direction="vertical" :reverse="false" :duration="0.8" ease="power3.out"
            :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0" class="h-full">
            <div class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10">
              <div class="text-4xl text-secondary mb-4">
                <i class="fas fa-network-wired"></i>
              </div>
              <h4 class="text-xl font-semibold mb-3">算力网络升级</h4>
              <p>计划三年内建成百万卡级智算集群，支持万卡并行训练，通过异构算力调度系统实现国产芯片利用率提升35%</p>
            </div>
          </AnimatedContent>

          <AnimatedContent :distance="50" direction="vertical" :reverse="false" :duration="0.8" ease="power3.out"
            :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0.2" class="h-full">
            <div class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10">
              <div class="text-4xl text-secondary mb-4">
                <i class="fas fa-industry"></i>
              </div>
              <h4 class="text-xl font-semibold mb-3">行业纵深拓展</h4>
              <p>推出行业智能体，实现企业重复作业上的降本增效，目标2026年覆盖80%主流工业场景</p>
            </div>
          </AnimatedContent>

          <AnimatedContent :distance="50" direction="vertical" :reverse="false" :duration="0.8" ease="power3.out"
            :initial-opacity="0" :animate-opacity="true" :scale="0.9" :threshold="0.1" :delay="0.4" class="h-full">
            <div class="bg-white/5 backdrop-blur-md rounded-xl p-6 border border-white/10">
              <div class="text-4xl text-secondary mb-4">
                <i class="fas fa-globe"></i>
              </div>
              <h4 class="text-xl font-semibold mb-3">生态价值闭环</h4>
              <p>构建"开发者-企业-政府"三位一体平台，通过AI技术加速技术商业化进程，力争2027年实现千亿级生态产值</p>
            </div>
          </AnimatedContent>
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden z-[-1]">
      <div class="absolute rounded-full bg-secondary/5 opacity-30 w-[400px] h-[400px] top-[-100px] left-[-100px]"></div>
      <div class="absolute rounded-full bg-secondary/5 opacity-30 w-[500px] h-[500px] bottom-[100px] right-[-200px]">
      </div>
      <div class="absolute rounded-full bg-secondary/5 opacity-30 w-[300px] h-[300px] bottom-[-150px] left-[200px]">
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { animate, stagger } from 'animejs';
import AnimatedContent from '../ui/animations/AnimatedContent/AnimatedContent.vue';
import FadeContent from '../ui/animations/FadeContent/FadeContent.vue';

// 组件挂载后初始化动画
onMounted(() => {
  if (process.client) {
    // 初始化动画
    animateAboutCompanySection();
  }
})

// 关于公司部分动画
function animateAboutCompanySection() {
  // 标题动画
  animate('#about-company .section-title', {
    translateY: [50, 0],
    opacity: [0, 1],
    duration: 1000,
    easing: 'easeOutQuad'
  });

  // 各个区块动画
  animate('#about-company .bg-white\\/5', {
    translateY: [50, 0],
    opacity: [0, 1],
    duration: 800,
    delay: stagger(200),
    easing: 'easeOutQuad'
  });

  // 时间线动画
  animate('#about-company .w-4.h-4', {
    scale: [0, 1],
    opacity: [0, 1],
    duration: 600,
    delay: stagger(300),
    easing: 'easeOutBack'
  });
}
</script>

<style scoped>
/* 响应式时间线 */
@media (max-width: 768px) {
  .timeline-line {
    left: 20px;
  }

  .timeline-item {
    flex-direction: column;
    text-align: left;
  }

  .timeline-content {
    margin-left: 40px;
    margin-top: 10px;
  }

  /* 发展历程卡片内容字体缩小 */
  .timeline-card,
  .timeline-card * {
    font-size: 0.95rem !important;
  }
}
</style>