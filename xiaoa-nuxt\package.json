{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host=0.0.0.0", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxtjs/robots": "^5.4.0", "@nuxtjs/seo": "^3.1.0", "@nuxtjs/tailwindcss": "^6.14.0", "@vueuse/motion": "^3.0.3", "animejs": "^4.0.2", "gsap": "^3.13.0", "lenis": "^1.3.8", "nuxt": "^4.0.0", "ogl": "^1.0.11", "vite-tsconfig-paths": "^5.1.4", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"sass": "^1.89.2", "typescript": "^5.8.3"}}