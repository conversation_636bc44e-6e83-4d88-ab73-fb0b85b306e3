<template>
  <section id="hero"
    class="section relative overflow-hidden min-h-[100vh] bg-cover bg-center before:content-[''] before:absolute before:inset-0 before:bg-black/30 before:z-[1]">
    <!-- 添加Threads背景 -->
    <div class="absolute inset-0 z-0">
      <img class="w-full h-full object-cover" src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/fitment/7月28日 (1).webp"
        alt="">
    </div>
    <div class="container mx-auto px-4 relative z-[2]">
      <div class="flex flex-wrap items-center -mx-4">
        <div class="w-full lg:w-2/5 px-4">
          <div class="transform translate-y-0 opacity-100 transition-all duration-1000">
            <SplitText text="小A AI引擎" :className="'hero-title mb-5'" :delay="150" :duration="0.8" ease="power3.out"
              splitType="chars" :from="{ opacity: 0, y: 50, rotationX: -90 }" :to="{ opacity: 1, y: 0, rotationX: 0 }"
              :threshold="0.1" textAlign="left" />
            <div class="hero-subtitle mb-7">
              <ShinyText text="引领未来交互方式，打造智能数字世界" :speed="3" className="" />
            </div>
            <button class="hero-btn">
              <nuxt-link to="/#contact">联系我们<i class="fas fa-arrow-right ml-2"></i></nuxt-link>
            </button>
          </div>
        </div>

        <div class="w-full lg:w-3/5 px-4 mt-8 lg:mt-0">
          <div class="transform translate-x-0 opacity-100 transition-all duration-1000 relative group">
            <video ref="videoRef"
              src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/videos/1753347186775_52c19d581fcaa0af0d23c20782662880.mp4"
              alt="数字人技术"
              class="w-full h-full rounded-2xl shadow-card transform origin-center hover:scale-105 transition-transform duration-500"
              loop playsinline>
            </video>

            <div
              class="absolute inset-0 bg-black/40 rounded-2xl flex items-center justify-center transition-all duration-300"
              :class="[isPlaying ? 'opacity-0 group-hover:opacity-100' : 'opacity-100']">
              <button @click="togglePlay"
                class="w-16 h-16 bg-white/20 hover:bg-white/40 rounded-full flex items-center justify-center transition-all transform hover:scale-110">
                <i :class="[isPlaying ? 'fas fa-pause' : 'fas fa-play', 'text-white text-2xl']"></i>
              </button>

              <div class="absolute bottom-4 right-4 flex items-center bg-white/10 rounded-full py-2 px-3">
                <button @click="toggleMute" class="mr-2 hover:text-secondary transition-colors">
                  <i
                    :class="[isMuted ? 'fas fa-volume-mute' : volume < 0.5 ? 'fas fa-volume-down' : 'fas fa-volume-up', 'text-white']"></i>
                </button>
                <input type="range" min="0" max="1" step="0.01" v-model="volume" @input="updateVolume"
                  class="w-20 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer volume-slider" />
                <span class="ml-2 text-white text-xs min-w-[32px]">{{ Math.round(volume * 100) }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="mt-16 w-full p-8 relative overflow-hidden bg-white/5 backdrop-blur-md rounded-xl shadow-card border border-white/10">
        <div class="overflow-hidden">
          <div class="logos-row logos-row-left">
            <div class="flex whitespace-nowrap animate-scroll-left">
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片1.png" alt="合作伙伴1"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片2.png" alt="合作伙伴2"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片3.png" alt="合作伙伴3"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片4.png" alt="合作伙伴4"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片5.png" alt="合作伙伴5"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片6.png" alt="合作伙伴6"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片7.png" alt="合作伙伴7"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片8.png" alt="合作伙伴8"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片9.png" alt="合作伙伴9"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片1.png" alt="合作伙伴1"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片2.png" alt="合作伙伴2"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片3.png" alt="合作伙伴3"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片4.png" alt="合作伙伴4"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
            </div>
          </div>
          <div class="logos-row logos-row-right mt-8">
            <div class="flex whitespace-nowrap animate-scroll-right">
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片10.png" alt="合作伙伴10"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片11.png" alt="合作伙伴11"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片12.png" alt="合作伙伴12"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片13.png" alt="合作伙伴13"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片14.png" alt="合作伙伴14"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片15.png" alt="合作伙伴15"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片16.png" alt="合作伙伴16"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片17.png" alt="合作伙伴17"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片19.png" alt="合作伙伴19"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片10.png" alt="合作伙伴10"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片11.png" alt="合作伙伴11"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片12.png" alt="合作伙伴12"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
              <div class="flex-none flex justify-center items-center px-8">
                <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/图片13.png" alt="合作伙伴13"
                  class="h-12 max-w-[150px] opacity-70 grayscale hover:opacity-100 hover:grayscale-0 hover:scale-105 transition-all duration-300">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 浮动形状元素 -->
    <div class="absolute top-[20%] left-[10%] w-[100px] h-[100px] opacity-40 z-[1] floating-element animate-float" />
    <div
      class="absolute bottom-[30%] right-[15%] w-[150px] h-[150px] opacity-40 z-[1] floating-element animate-float-delay" />
    <div class="absolute top-[60%] left-[20%] w-[80px] h-[80px] opacity-40 z-[1] floating-element animate-float-slow" />
  </section>
</template>

<script setup lang="ts">
import ShinyText from '~/components/ui/textAnimations/ShinyText/ShinyText.vue'
import SplitText from '~/components/ui/textAnimations/SplitText/SplitText.vue'
import { ref, onMounted } from 'vue'

// 视频控制相关
const videoRef = ref<HTMLVideoElement | null>(null)
const isPlaying = ref(false)
const isMuted = ref(false) // 默认非静音
const volume = ref(0.7) // 默认音量稍大一些

// 播放/暂停切换
const togglePlay = () => {
  if (!videoRef.value) return

  if (isPlaying.value) {
    videoRef.value.pause()
  } else {
    videoRef.value.play()
  }

  isPlaying.value = !isPlaying.value
}

// 静音/取消静音切换
const toggleMute = () => {
  if (!videoRef.value) return

  videoRef.value.muted = !videoRef.value.muted
  isMuted.value = videoRef.value.muted
}

// 更新音量
const updateVolume = () => {
  if (!videoRef.value) return

  videoRef.value.volume = volume.value
  if (volume.value > 0 && isMuted.value) {
    videoRef.value.muted = false
    isMuted.value = false
  }
}

// 组件挂载时设置初始状态
onMounted(() => {
  if (videoRef.value) {
    // 设置初始音频状态
    videoRef.value.muted = isMuted.value
    videoRef.value.volume = volume.value
  }
})
</script>

<style scoped>
.floating-element {
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  backdrop-filter: blur(8px);
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0);
  }

  50% {
    transform: translateY(-20px) rotate(5deg);
  }

  100% {
    transform: translateY(0) rotate(0);
  }
}

@keyframes float-delay {
  0% {
    transform: translateY(0) rotate(0);
  }

  50% {
    transform: translateY(-15px) rotate(-5deg);
  }

  100% {
    transform: translateY(0) rotate(0);
  }
}

@keyframes float-slow {
  0% {
    transform: translateY(0) rotate(0);
  }

  50% {
    transform: translateY(-10px) rotate(3deg);
  }

  100% {
    transform: translateY(0) rotate(0);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delay {
  animation: float-delay 8s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite;
}

.hero-title {
  @apply text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  @apply text-xl md:text-2xl text-gray-200 mb-8;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.hero-btn {
  @apply px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-medium transition-all hover:shadow-lg hover:-translate-y-1 focus:outline-none;
}

/* 音量滑块样式 */
.volume-slider::-webkit-slider-thumb {
  @apply appearance-none w-3 h-3 rounded-full bg-white hover:bg-secondary cursor-pointer;
}

.volume-slider::-moz-range-thumb {
  @apply w-3 h-3 border-none rounded-full bg-white hover:bg-secondary cursor-pointer;
}
</style>
