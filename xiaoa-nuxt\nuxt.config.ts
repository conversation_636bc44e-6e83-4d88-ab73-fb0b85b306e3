// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  srcDir: 'src/',
  devtools: { enabled: true },
  // 开启服务端渲染
  ssr: true,
  // 移除SCSS引入，替换为Tailwind
  css: [
    '@/assets/css/main.css',
  ],
  // 配置运行时环境
  runtimeConfig: {
    // 服务器端
    app: {
      host: '0.0.0.0', // 监听所有网络接口
      port: 3000
    }
  },
  modules: [
    '@nuxtjs/robots',
    '@nuxtjs/seo',
    '@nuxtjs/tailwindcss',
    '@vueuse/motion/nuxt'
  ],
  // robots配置 - 暂时注释，等待查看正确的文档格式
  // robots: {
  //   rules: [
  //     { UserAgent: '*', Allow: '/' },
  //     { Sitemap: 'https://xiaoa.example.com/sitemap.xml' }
  //   ]
  // },
  app: {
    head: {
      title: '小A引擎 - 数字人智能体系统',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        {
          name: 'description',
          content: '小A引擎是引领未来交互方式的智能数字人系统，提供数字人、分发系统、智能体和全平台客户端解决方案。'
        },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'theme-color', content: '#3a0ca3' },
        { name: 'robots', content: 'index, follow' },
        { property: 'og:type', content: 'website' },
        { property: 'og:title', content: '小A引擎 - 数字人智能体系统' },
        {
          property: 'og:description',
          content: '小A引擎是引领未来交互方式的智能数字人系统，提供数字人、分发系统、智能体和全平台客户端解决方案。'
        },
        { property: 'og:image', content: '/og-image.jpg' },
        { property: 'og:url', content: 'https://xiaoa.example.com' },
        { name: 'twitter:card', content: 'summary_large_image' },
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'stylesheet', href: 'https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css' }
      ],
      script: [
        // 可选的外部脚本
      ]
    }
  },
  // 移除SCSS预处理器配置
  // vite: {
  //   css: {
  //     preprocessorOptions: {
  //       scss: {
  //         additionalData: '@import "~/assets/scss/_variables.scss"; @import "~/assets/scss/_mixins.scss";' // 修正路径，使用 ~ 前缀
  //       }
  //     }
  //   }
  // },
  routeRules: {
    // 调整为服务端渲染模式
    '/': { ssr: true }
  },
  nitro: {
    // 生产环境下的配置
    compressPublicAssets: true,
    // 添加兼容性日期
    compatibilityDate: '2025-07-19',
    // 服务端渲染优化
    prerender: {
      crawlLinks: true,
      routes: ['/']
    }
  },
  // 配置Tailwind CSS
  tailwindcss: {
    cssPath: '~/assets/css/main.css',
    configPath: './tailwind.config.js',
    exposeConfig: false,
    viewer: true,
  }
})
