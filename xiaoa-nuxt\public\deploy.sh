#!/bin/bash
set -e

echo "开始部署小A引擎官网..."

# 停止现有服务
echo "停止现有服务..."
pm2 stop official_website || true
pm2 delete official_website || true

# 备份当前版本
if [ -d "/usr/local/dr/web/current" ]; then
    mv /usr/local/dr/web/current /usr/local/dr/web/backup_$(date +%Y%m%d_%H%M%S)
fi

# 创建新的部署目录
mkdir -p /usr/local/dr/web/current

# 解压制品包
echo "解压制品包..."
tar -xzf /usr/local/dr/web/package.tgz -C /usr/local/dr/web/current

# 进入应用目录并启动
cd /usr/local/dr/web/current
chmod +x ./server/index.mjs

echo "启动应用..."
PORT=8200 pm2 start ./server/index.mjs --name official_website

# 健康检查
sleep 10
if curl -f http://localhost:8200 > /dev/null 2>&1; then
    echo "✅ 部署成功！"
    ls -t /usr/local/dr/web/backup_* 2>/dev/null | tail -n +4 | xargs rm -rf 2>/dev/null || true
else
    echo "❌ 健康检查失败"
    exit 1
fi

pm2 status
echo "部署完成！"