<template>
  <section id="ai-agent" class="section">
    <div class="container">
      <h2 class="section-title">强大的<span>智能体</span>技术</h2>

      <div class="row mb-5">
        <div class="col-lg-8 mx-auto text-center">
          <p class="text-lg opacity-90 mb-12">我们的智能体系统结合了前沿AI技术，为数字人赋能，使其具备理解、思考、决策和学习的能力，可应用于多种复杂场景。</p>
        </div>
      </div>

      <!-- 智能体大脑和模块部分 -->
      <div class="flex flex-col items-center mb-16 relative z-10">
        <!-- 中央大脑部分 -->
        <div
          class="w-[200px] h-[200px] rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center relative shadow-xl mb-20 agent-brain">
          <div
            class="w-[50px] h-[50px] bg-white rounded-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
          </div>
          <!-- 放射状连接线 -->
          <div class="absolute inset-0 connection-lines">
            <div class="absolute w-[2px] h-[100px] bg-white/30 top-full left-1/2 -translate-x-1/2 connection-line-vertical"></div>
            <div class="absolute w-[2px] h-[60px] bg-white/30 -rotate-45 top-[80%] left-[80%] connection-line-diagonal"></div>
            <div class="absolute w-[2px] h-[60px] bg-white/30 rotate-45 top-[80%] right-[80%] connection-line-diagonal"></div>
            <div class="absolute w-[2px] h-[60px] bg-white/30 -rotate-[135deg] bottom-[80%] left-[80%] connection-line-diagonal"></div>
            <div class="absolute w-[2px] h-[60px] bg-white/30 rotate-[135deg] bottom-[80%] right-[80%] connection-line-diagonal"></div>
          </div>
          
          <!-- 脉冲点 -->
          <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center">
            <div class="w-[10px] h-[10px] bg-white rounded-full mb-[10px] connection-line"></div>
            <div class="w-[10px] h-[10px] bg-white rounded-full mb-[10px] connection-line"></div>
            <div class="w-[10px] h-[10px] bg-white rounded-full mb-[10px] connection-line"></div>
            <div class="w-[10px] h-[10px] bg-white rounded-full mb-[10px] connection-line"></div>
          </div>
        </div>

        <!-- 模块卡片 -->
        <div class="flex flex-wrap justify-center gap-8 w-full agent-modules-container">
          <div
            class="bg-white/5 backdrop-blur-md rounded-xl p-6 text-center transition-all duration-500 border border-white/10 hover:translate-y-[-10px] hover:shadow-lg hover:border-secondary w-full sm:w-[280px] agent-module"
            data-index="0">
            <div class="text-5xl mb-4 text-secondary">
              <i class="fas fa-comment-dots"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3">语言理解</h3>
            <p class="text-light/80 text-sm">先进的NLP技术，让智能体理解用户意图，实现自然流畅的多轮对话。</p>
          </div>

          <div
            class="bg-white/5 backdrop-blur-md rounded-xl p-6 text-center transition-all duration-500 border border-white/10 hover:translate-y-[-10px] hover:shadow-lg hover:border-secondary w-full sm:w-[280px] agent-module"
            data-index="1">
            <div class="text-5xl mb-4 text-secondary">
              <i class="fas fa-lightbulb"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3">知识推理</h3>
            <p class="text-light/80 text-sm">基于大规模知识库和推理引擎，可进行深度分析和逻辑推理，解决复杂问题。</p>
          </div>

          <div
            class="bg-white/5 backdrop-blur-md rounded-xl p-6 text-center transition-all duration-500 border border-white/10 hover:translate-y-[-10px] hover:shadow-lg hover:border-secondary w-full sm:w-[280px] agent-module"
            data-index="2">
            <div class="text-5xl mb-4 text-secondary">
              <i class="fas fa-sync"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3">持续学习</h3>
            <p class="text-light/80 text-sm">自适应学习系统不断从交互中获取经验，提升服务质量和个性化水平。</p>
          </div>

          <div
            class="bg-white/5 backdrop-blur-md rounded-xl p-6 text-center transition-all duration-500 border border-white/10 hover:translate-y-[-10px] hover:shadow-lg hover:border-secondary w-full sm:w-[280px] agent-module"
            data-index="3">
            <div class="text-5xl mb-4 text-secondary">
              <i class="fas fa-tools"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3">工具调用</h3>
            <p class="text-light/80 text-sm">能够调用各种API和工具，执行任务并获取实时信息，实现更强大的功能。</p>
          </div>
        </div>
      </div>

      <div class="row mt-5">
        <!-- 重新布局为三列：场景应用、图片、核心优势 -->
        <div class="flex flex-col lg:flex-row gap-6">
          <!-- 场景应用 -->
          <div class="w-full lg:w-1/3">
          <div
              class="bg-white/5 backdrop-blur-md rounded-xl p-6 h-full transition-all duration-500 border border-white/10 hover:translate-y-[-10px] hover:shadow-lg hover:border-secondary opacity-0 translate-y-12 agent-card">
            <h3 class="text-2xl mb-5 text-secondary">场景应用</h3>
            <ul class="list-none p-0 m-0">
              <li class="flex items-center mb-4 text-light">
                <span class="text-xl mr-3 text-secondary"><i class="fas fa-user-tie"></i></span>
                <span class="opacity-90">智能客服与销售顾问</span>
              </li>
              <li class="flex items-center mb-4 text-light">
                <span class="text-xl mr-3 text-secondary"><i class="fas fa-chalkboard-teacher"></i></span>
                <span class="opacity-90">个性化教育助手</span>
              </li>
              <li class="flex items-center mb-4 text-light">
                <span class="text-xl mr-3 text-secondary"><i class="fas fa-hospital"></i></span>
                <span class="opacity-90">健康咨询与医疗辅助</span>
              </li>
              <li class="flex items-center mb-4 text-light">
                <span class="text-xl mr-3 text-secondary"><i class="fas fa-shopping-cart"></i></span>
                <span class="opacity-90">电商导购与推荐</span>
              </li>
            </ul>
          </div>
        </div>
          
          <!-- 智能体技术图 -->
          <div class="w-full lg:w-1/3 flex items-center justify-center">
            <div class="w-[64%] aspect-[9/16] relative opacity-0 translate-y-12 agent-card">
              <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/95e5ebb5d8ea23505953cf3d6da1147.png" 
                alt="智能体技术图" 
                class="w-full h-full object-cover rounded-lg shadow-lg border border-white/10 transition-all duration-500 hover:shadow-2xl hover:border-secondary/50" />
            </div>
          </div>
          
          <!-- 核心优势 -->
          <div class="w-full lg:w-1/3">
            <div
              class="bg-white/5 backdrop-blur-md rounded-xl p-6 h-full transition-all duration-500 border border-white/10 hover:translate-y-[-10px] hover:shadow-lg hover:border-secondary opacity-0 translate-y-12 agent-card">
            <h3 class="text-2xl mb-5 text-secondary">核心优势</h3>
            <ul class="list-none p-0 m-0">
              <li class="flex items-center mb-4 text-light">
                <span class="text-xl mr-3 text-secondary"><i class="fas fa-robot"></i></span>
                <span class="opacity-90">高效自动化处理能力：替代重复性工作，释放人力价值</span>
              </li>
              <li class="flex items-center mb-4 text-light">
                <span class="text-xl mr-3 text-secondary"><i class="fas fa-fingerprint"></i></span>
                <span class="opacity-90">精准个性化服务：基于数据与场景的智能适配</span>
              </li>
              <li class="flex items-center mb-4 text-light">
                <span class="text-xl mr-3 text-secondary"><i class="fas fa-network-wired"></i></span>
                <span class="opacity-90">灵活扩展与跨平台整合：快速适配业务变化，触达多端用户</span>
              </li>
              </ul>
                </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 背景元素 -->
    <div class="absolute inset-0 overflow-hidden z-[-1]">
      <div class="absolute rounded-full bg-secondary/5 opacity-50 w-[300px] h-[300px] top-[-100px] left-[-100px] bg-circle"></div>
      <div class="absolute rounded-full bg-secondary/5 opacity-50 w-[400px] h-[400px] bottom-[100px] right-[-200px] bg-circle">
      </div>
      <div class="absolute rounded-full bg-secondary/5 opacity-50 w-[250px] h-[250px] bottom-[-150px] right-[200px] bg-circle">
      </div>
    </div>

    <div class="absolute inset-0 overflow-hidden z-[1] pointer-events-none">
      <div class="absolute w-[10px] h-[10px] bg-secondary rounded-full opacity-0"></div>
      <div class="absolute w-[10px] h-[10px] bg-secondary rounded-full opacity-0"></div>
      <div class="absolute w-[10px] h-[10px] bg-secondary rounded-full opacity-0"></div>
      <div class="absolute w-[10px] h-[10px] bg-secondary rounded-full opacity-0"></div>
      <div class="absolute w-[10px] h-[10px] bg-secondary rounded-full opacity-0"></div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';

// 组件挂载后初始化动画
onMounted(() => {
  if (process.client) {
    // 使用setTimeout确保DOM已完全加载
    setTimeout(() => {
      initAgentAnimations();
    }, 300);
  }
})

// 初始化智能体动画
function initAgentAnimations() {
  // 添加初始类，确保元素有正确的起始状态
  const brain = document.querySelector('.agent-brain');
  if (brain) {
    brain.classList.add('opacity-0', 'scale-90');
    
    // 延迟后添加动画类
    setTimeout(() => {
      brain.classList.add('animate-fade-scale-in');
      brain.classList.remove('opacity-0', 'scale-90');
    }, 100);
  }
  
  // 连接线动画
  const connectionLines = document.querySelectorAll('.connection-line');
  connectionLines.forEach((line, index) => {
    line.classList.add('opacity-0', 'scale-0');
    
    setTimeout(() => {
      line.classList.add('animate-pulse');
      line.classList.remove('opacity-0', 'scale-0');
    }, 500 + (index * 150));
  });
  
  // 垂直连接线动画
  const verticalLine = document.querySelector('.connection-line-vertical');
  if (verticalLine) {
    verticalLine.classList.add('opacity-0', 'scale-y-0', 'origin-top');
    
    setTimeout(() => {
      verticalLine.classList.add('animate-grow-line');
      verticalLine.classList.remove('opacity-0', 'scale-y-0');
    }, 400);
  }
  
  // 对角线连接线动画
  const diagonalLines = document.querySelectorAll('.connection-line-diagonal');
  diagonalLines.forEach((line, index) => {
    line.classList.add('opacity-0', 'scale-y-0', 'origin-bottom');
    
    setTimeout(() => {
      line.classList.add('animate-grow-line');
      line.classList.remove('opacity-0', 'scale-y-0');
    }, 600 + (index * 100));
  });
  
  // 模块卡片动画
  const moduleCards = document.querySelectorAll('.agent-module');
  moduleCards.forEach((card, index) => {
    card.classList.add('opacity-0', 'translate-y-8');
    
    setTimeout(() => {
      card.classList.add('animate-slide-up-fade');
      card.classList.remove('opacity-0', 'translate-y-8');
    }, 1000 + (index * 200));
  });
  
  // 额外卡片动画
  const agentCards = document.querySelectorAll('.agent-card');
  agentCards.forEach((card, index) => {
    card.classList.add('opacity-0', 'translate-y-12');
    
    setTimeout(() => {
      card.classList.add('animate-slide-up');
      card.classList.remove('opacity-0', 'translate-y-12');
    }, 1800 + (index * 200));
  });
  
  // 背景圆圈动画
  const bgCircles = document.querySelectorAll('.bg-circle');
  bgCircles.forEach((circle, index) => {
    circle.classList.add('opacity-0', 'scale-50');
    
    setTimeout(() => {
      circle.classList.add('animate-scale-in');
      circle.classList.remove('opacity-0', 'scale-50');
    }, 200 + (index * 200));
  });
}
</script>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-fade-scale-in {
  animation: fadeScaleIn 0.8s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
}

.animate-slide-up-fade {
  animation: slideUpFade 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.8s ease-out forwards;
}

.animate-grow-line {
  animation: growLine 0.6s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeScaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUpFade {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.5);
  }
  to { 
    opacity: 0.5;
    transform: scale(1);
  }
}

@keyframes growLine {
  from {
    opacity: 0;
    transform: scaleY(0);
  }
  to {
    opacity: 0.3;
    transform: scaleY(1);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.7;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.7;
    transform: scale(0.8);
  }
}
</style>