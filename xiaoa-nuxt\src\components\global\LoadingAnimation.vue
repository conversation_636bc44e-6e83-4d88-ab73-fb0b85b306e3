<template>
  <div class="loader fixed inset-0 bg-dark flex justify-center items-center z-[9999] transition-opacity duration-1000" :class="{ 'opacity-0 pointer-events-none': isLoaded }">
    <div class="flex flex-col items-center">
      <div class="w-[50px] h-[50px] border-4 border-white/10 border-t-accent rounded-full mb-5 animate-spin"></div>
      <h3 class="mt-4 text-xl font-light text-light">数字未来，正在加载...</h3>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 是否已加载完成
const isLoaded = ref(false);

// 在客户端检测所有资源加载完成
onMounted(() => {
  // 如果文档已经加载完成
  if (document.readyState === 'complete') {
    completeLoading();
  } else {
    // 否则等待所有资源加载完成
    window.addEventListener('load', completeLoading);
  }
});

// 完成加载处理
const completeLoading = () => {
  // 将状态设置为已加载
  isLoaded.value = true;
  
  // 动画结束后从DOM中移除加载器
  setTimeout(() => {
    const loaderElement = document.querySelector('.loader');
    if (loaderElement && loaderElement.parentNode) {
      loaderElement.parentNode.removeChild(loaderElement);
    }
  }, 1000); // 淡出动画时长
};
</script> 