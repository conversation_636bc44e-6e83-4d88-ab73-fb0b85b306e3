<template>
  <section id="smart-phone" class="section bg-gradient-secondary relative overflow-hidden">
    <div class="container mx-auto px-4">
      <h2 class="section-title transform translate-y-0 opacity-100 transition-all duration-1000">
        智能<span>手机</span>引流方案
      </h2>

      <div class="flex flex-wrap items-center -mx-4">
        <!-- 移动端优先，在小屏幕时图片在上，文字在下 -->
        <div class="w-full lg:w-1/3 px-4 mb-10 lg:mb-0 order-1 lg:order-2">
          <div class="flex justify-center lg:justify-end">
            <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/9f1195484fa82843e72e6cf3272d070.png" alt="智能手机展示"
              class="w-auto h-auto max-h-[500px] lg:max-h-[600px] rounded-2xl shadow-card transform translate-x-0 opacity-100 transition-all duration-1000" />
          </div>
        </div>

        <div class="w-full lg:w-2/3 px-4 order-2 lg:order-1">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="py-3">
              <FadeContent
                :blur="true"
                :duration="800"
                :delay="0"
                :threshold="0.1"
                :initial-opacity="0"
                easing="ease-out"
                class-name="h-full"
              >
                <div class="feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-headset"></i>
                  </div>
                  <h3 class="feature-title">引流客服一体</h3>
                  <p>智能识别相关视频品类，微信号传送到品牌方，自动加微，智能沟通答疑</p>
                </div>
              </FadeContent>
            </div>

            <div class="py-3">
              <FadeContent
                :blur="true"
                :duration="800"
                :delay="200"
                :threshold="0.1"
                :initial-opacity="0"
                easing="ease-out"
                class-name="h-full"
              >
                <div class="feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-robot"></i>
                  </div>
                  <h3 class="feature-title">AI智能体助手</h3>
                  <p>数据投喂，全能解答，成为最了解公司产品的人，自动互动朋友圈</p>
                </div>
              </FadeContent>
            </div>

            <div class="py-3">
              <FadeContent
                :blur="true"
                :duration="800"
                :delay="400"
                :threshold="0.1"
                :initial-opacity="0"
                easing="ease-out"
                class-name="h-full"
              >
                <div class="feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-ad"></i>
                  </div>
                  <h3 class="feature-title">广告自动分发</h3>
                  <p>接单、分发一站式搞定，提升广告效率与投放精准度</p>
                </div>
              </FadeContent>
            </div>

            <div class="py-3">
              <FadeContent
                :blur="true"
                :duration="800"
                :delay="600"
                :threshold="0.1"
                :initial-opacity="0"
                easing="ease-out"
                class-name="h-full"
              >
                <div class="feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                  </div>
                  <h3 class="feature-title">账号智能养成</h3>
                  <p>自动化提升账号质量，打造优质流量池</p>
                </div>
              </FadeContent>
            </div>

            <div class="py-3">
              <FadeContent
                :blur="true"
                :duration="800"
                :delay="800"
                :threshold="0.1"
                :initial-opacity="0"
                easing="ease-out"
                class-name="h-full"
              >
                <div class="feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-graduation-cap"></i>
                  </div>
                  <h3 class="feature-title">销冠专业培训</h3>
                  <p>助力用户成为营销高手，提供系统化营销知识</p>
                </div>
              </FadeContent>
            </div>

            <div class="py-3">
              <FadeContent
                :blur="true"
                :duration="800"
                :delay="1000"
                :threshold="0.1"
                :initial-opacity="0"
                easing="ease-out"
                class-name="h-full"
              >
                <div class="feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-sync-alt"></i>
                  </div>
                  <h3 class="feature-title">功能持续更新</h3>
                  <p>月更1-2项新功能，持续优化用户体验</p>
                </div>
              </FadeContent>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 悬浮元素 -->
    <div
      class="absolute top-[15%] left-[10%] w-[120px] h-[120px] rounded-full bg-primary/10 flex items-center justify-center font-semibold transform scale-100 opacity-70 transition-all duration-1000">
      智能引流
    </div>

    <div
      class="absolute bottom-[20%] right-[15%] w-[120px] h-[120px] rounded-full bg-primary/10 flex items-center justify-center font-semibold transform scale-100 opacity-70 transition-all duration-1000 delay-200">
      自动化运营
    </div>

    <div
      class="absolute top-[50%] left-[15%] w-[100px] h-[100px] rounded-full bg-primary/10 flex items-center justify-center font-semibold transform scale-100 opacity-70 transition-all duration-1000 delay-400">
      持续更新
    </div>
  </section>
</template>

<script setup>
import FadeContent from '../ui/animations/FadeContent/FadeContent.vue'
</script>

<style scoped>
/* 如果需要额外的样式可以在这里添加 */
</style>