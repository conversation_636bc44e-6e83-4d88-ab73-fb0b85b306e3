/** @type {import('tailwindcss').Config} */
module.exports = {
  // 指定Tailwind应处理的文件
  content: [
    './src/components/**/*.{js,vue,ts}',
    './src/layouts/**/*.vue',
    './src/pages/**/*.vue',
    './src/plugins/**/*.{js,ts}',
    './src/app.vue',
    './src/error.vue',
  ],
  // 暗色模式配置
  darkMode: 'class',
  theme: {
    extend: {
      // 扩展颜色系统，匹配index.html中的颜色变量
      colors: {
        'primary': '#3a0ca3', // --primary-color
        'secondary': '#4cc9f0', // --secondary-color
        'accent': '#f72585', // --accent-color
        'dark': '#1a1a2e', // --dark-color
        'light': '#f8f9fa', // --light-color
      },
      // 扩展字体系统
      fontFamily: {
        sans: ['Segoe UI', 'Tahoma', 'Geneva', 'Verdana', 'sans-serif'],
      },
      // 自定义背景渐变
      backgroundImage: {
        'hero-pattern': "url('https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/c3ef89cca0f08b7ab892332a3acd100%20(1).png')",
        'gradient-primary': 'linear-gradient(45deg, #1a1a2e, #2d1b69)',
        'gradient-secondary': 'linear-gradient(45deg, #1a1a2e, #261447)',
        'gradient-accent': 'linear-gradient(45deg, var(--primary-color), var(--accent-color))',
        'gradient-text': 'linear-gradient(90deg, #4cc9f0, #f72585)',
      },
      // 自定义动画
      animation: {
        'scroll-left': 'scroll-left 60s linear infinite',
        'scroll-right': 'scroll-right 60s linear infinite',
        'float': 'float 6s ease-in-out infinite',
        'spin': 'spin 1s linear infinite',
      },
      keyframes: {
        'scroll-left': {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-50%)' },
        },
        'scroll-right': {
          '0%': { transform: 'translateX(-50%)' },
          '100%': { transform: 'translateX(0)' },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' },
        },
      },
      // 自定义阴影
      boxShadow: {
        'card': '0 20px 40px rgba(0, 0, 0, 0.3)',
        'button': '0 10px 20px rgba(247, 37, 133, 0.3)',
        'nav': '0 8px 32px rgba(0, 0, 0, 0.1)',
      },
      // 自定义模糊效果
      backdropBlur: {
        'md': '10px',
        'lg': '15px',
      },
      // 自定义边框半径
      borderRadius: {
        'xl': '20px',
        '2xl': '30px',
      },
      // 自定义过渡效果
      transitionDuration: {
        '2000': '2000ms',
        '3000': '3000ms',
      },
    },
    // 自定义容器
    container: {
      center: true,
      padding: {
        DEFAULT: '1rem',
        sm: '2rem',
        lg: '4rem',
        xl: '5rem',
      },
    },
  },
  plugins: [
    // 添加自定义Tailwind插件
    function({ addBase, theme }) {
      addBase({
        // 设置根元素CSS变量
        ':root': {
          '--primary-color': theme('colors.primary'),
          '--secondary-color': theme('colors.secondary'),
          '--accent-color': theme('colors.accent'),
          '--dark-color': theme('colors.dark'),
          '--light-color': theme('colors.light'),
        },
        // 基础样式
        'body': {
          backgroundColor: theme('colors.dark'),
          color: theme('colors.light'),
          overflowX: 'hidden',
        }
      });
    },
  ],
} 