<template>
  <section id="digital-human" class="section bg-gradient-primary relative overflow-hidden">
    <div class="container mx-auto px-4">
      <h2 class="section-title transform translate-y-0 opacity-100 transition-all duration-1000">
        先进的<span>数字人</span>技术
      </h2>
      
      <div class="flex flex-wrap items-center -mx-4">
        <div class="w-full lg:w-1/2 px-4 mb-10 lg:mb-0">
          <img 
            src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/%E5%88%86%E8%A7%A3%E8%83%8C%E6%99%AF%E5%9B%BE.png" 
            alt="数字人展示" 
            class="w-full rounded-2xl shadow-card transform translate-x-0 opacity-100 transition-all duration-1000"
          />
        </div>
        
        <div class="w-full lg:w-1/2 px-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="py-4">
              <AnimatedContent
                :distance="50"
                direction="vertical"
                :reverse="false"
                :duration="0.8"
                ease="power3.out"
                :initial-opacity="0"
                :animate-opacity="true"
                :scale="0.9"
                :threshold="0.1"
                :delay="0"
                @complete="handleAnimationComplete"
                class="h-full"
              >
                <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-clone"></i>
                </div>
                <h3 class="feature-title">4.0真人级复刻</h3>
                <p>业界领先的Ai 4.0超高清克隆技术，拒绝「假脸」效果，呈现真实自然的数字人形象。</p>
              </div>
              </AnimatedContent>
            </div>
            
            <div class="py-4">
              <AnimatedContent
                :distance="50"
                direction="vertical"
                :reverse="false"
                :duration="0.8"
                ease="power3.out"
                :initial-opacity="0"
                :animate-opacity="true"
                :scale="0.9"
                :threshold="0.1"
                :delay="0.2"
                @complete="handleAnimationComplete"
                class="h-full"
              >
                <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-fire"></i>
                </div>
                <h3 class="feature-title">热点捕手</h3>
                <p>内置全网实时热点库，百业爆款文案，智能捕捉流行趋势，让内容始终保持新鲜度。</p>
              </div>
              </AnimatedContent>
            </div>
            
            <div class="py-4">
              <AnimatedContent
                :distance="50"
                direction="vertical"
                :reverse="false"
                :duration="0.8"
                ease="power3.out"
                :initial-opacity="0"
                :animate-opacity="true"
                :scale="0.9"
                :threshold="0.1"
                :delay="0.4"
                @complete="handleAnimationComplete"
                class="h-full"
              >
                <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-bolt"></i>
                </div>
                <h3 class="feature-title">分钟级产出</h3>
                <p>10分钟完成从创意到成片，效率提升50倍，极速满足各类内容创作需求。</p>
              </div>
              </AnimatedContent>
            </div>
            
            <div class="py-4">
              <AnimatedContent
                :distance="50"
                direction="vertical"
                :reverse="false"
                :duration="0.8"
                ease="power3.out"
                :initial-opacity="0"
                :animate-opacity="true"
                :scale="0.9"
                :threshold="0.1"
                :delay="0.6"
                @complete="handleAnimationComplete"
                class="h-full"
              >
                <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-piggy-bank"></i>
                </div>
                <h3 class="feature-title">成本运作</h3>
                <p>无需设备/团队，单账号省成本，让数字人技术触手可及，为企业创造更高投资回报。</p>
              </div>
              </AnimatedContent>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 技术圆圈 -->
    <div 
      class="absolute top-[20%] left-[10%] w-[150px] h-[150px] rounded-full bg-secondary/10 flex items-center justify-center font-semibold transform scale-100 opacity-70 transition-all duration-1000"
    >
      AI 4.0技术
    </div>
    
    <div 
      class="absolute bottom-[30%] right-[15%] w-[150px] h-[150px] rounded-full bg-secondary/10 flex items-center justify-center font-semibold transform scale-100 opacity-70 transition-all duration-1000 delay-200"
    >
      效率提升50倍
    </div>
    
    <div 
      class="absolute top-[60%] left-[20%] w-[150px] h-[150px] rounded-full bg-secondary/10 flex items-center justify-center font-semibold transform scale-100 opacity-70 transition-all duration-1000 delay-400"
    >
      全网热点库
    </div>
  </section>
</template>

<script setup>
import AnimatedContent from '../ui/animations/AnimatedContent/AnimatedContent.vue'

const handleAnimationComplete = () => {
  console.log("Feature card animation completed!")
}
</script> 