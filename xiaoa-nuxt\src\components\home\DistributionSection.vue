<template>
  <section id="distribution" class="relative section py-16 overflow-hidden">
    <div class="container mx-auto px-4">
      <h2 class="section-title text-center text-4xl font-bold mb-16 fade-in-down">智能<span class="text-primary">分发系统</span></h2>
      
      <!-- 主内容区域 -->
      <div class="flex flex-col lg:flex-row items-center">
        <!-- 左侧图片 -->
        <div class="w-full lg:w-1/2 mb-10 lg:mb-0 fade-in-up">
          <div class="relative">
            <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/分发系统.png" alt="分发系统" class="w-full rounded-xl shadow-2xl">
            
            <!-- 热点标记 -->
            <!-- <div class="absolute inset-0">
              <div 
                class="hotspot absolute w-6 h-6 bg-primary rounded-full cursor-pointer z-10 hover:scale-150 transition-all duration-300 flex items-center justify-center"
                style="top: 20%; left: 25%;">
                <div class="absolute w-12 h-12 bg-primary rounded-full ping-effect opacity-50"></div>
                <i class="fas fa-bolt text-white text-xs"></i>
              </div>
              
              <div
                class="hotspot absolute w-6 h-6 bg-primary rounded-full cursor-pointer z-10 hover:scale-150 transition-all duration-300 flex items-center justify-center"
                style="top: 40%; left: 70%;">
                <div class="absolute w-12 h-12 bg-primary rounded-full ping-effect opacity-50"></div>
                <i class="fas fa-random text-white text-xs"></i>
              </div>
              
              <div
                class="hotspot absolute w-6 h-6 bg-primary rounded-full cursor-pointer z-10 hover:scale-150 transition-all duration-300 flex items-center justify-center"
                style="top: 70%; left: 30%;">
                <div class="absolute w-12 h-12 bg-primary rounded-full ping-effect opacity-50"></div>
                <i class="fas fa-chart-line text-white text-xs"></i>
              </div>
              
              <div
                class="hotspot absolute w-6 h-6 bg-primary rounded-full cursor-pointer z-10 hover:scale-150 transition-all duration-300 flex items-center justify-center"
                style="top: 60%; left: 80%;">
                <div class="absolute w-12 h-12 bg-primary rounded-full ping-effect opacity-50"></div>
                <i class="fas fa-shield-alt text-white text-xs"></i>
              </div>
            </div> -->
          </div>
        </div>
        
        <!-- 右侧内容 -->
        <div class="w-full lg:w-1/2 lg:pl-12">
          <div class="space-y-10">
            <div class="feature-item slide-in-right bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all" style="--delay: 0.1s">
              <div class="flex items-start">
                <div class="bg-gradient-to-r from-primary to-accent w-[60px] h-[60px] rounded-full flex items-center justify-center mr-5 flex-shrink-0">
                  <i class="fas fa-bolt text-xl text-white"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold mb-2 text-secondary">效率革命</h3>
                  <p class="text-gray-600">1人管理500账号，AI日产千条视频</p>
                </div>
              </div>
            </div>
            
            <div class="feature-item slide-in-right bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all" style="--delay: 0.3s">
              <div class="flex items-start">
                <div class="bg-gradient-to-r from-primary to-accent w-[60px] h-[60px] rounded-full flex items-center justify-center mr-5 flex-shrink-0">
                  <i class="fas fa-bullseye text-xl text-white"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold mb-2 text-secondary">精准获客</h3>
                  <p class="text-gray-600">私域线索转化率提升3-5倍</p>
                </div>
              </div>
            </div>
            
            <div class="feature-item slide-in-right bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all" style="--delay: 0.5s">
              <div class="flex items-start">
                <div class="bg-gradient-to-r from-primary to-accent w-[60px] h-[60px] rounded-full flex items-center justify-center mr-5 flex-shrink-0">
                  <i class="fas fa-coins text-xl text-white"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold mb-2 text-secondary">成本重构</h3>
                  <p class="text-gray-600">人力成本降低80%，素材复用率提升90%</p>
                </div>
              </div>
            </div>
            
            <div class="feature-item slide-in-right bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all" style="--delay: 0.7s">
              <div class="flex items-start">
                <div class="bg-gradient-to-r from-primary to-accent w-[60px] h-[60px] rounded-full flex items-center justify-center mr-5 flex-shrink-0">
                  <i class="fas fa-shield-alt text-xl text-white"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold mb-2 text-secondary">风险屏障</h3>
                  <p class="text-gray-600">原创度检测+违禁词拦截双重防护</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 流动路径背景 -->
    <div class="absolute bottom-0 left-0 w-full h-full overflow-hidden opacity-20 z-1 pointer-events-none">
      <svg class="absolute w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
        <path class="path path-1" d="M0,50 Q20,20 40,50 T80,50 T100,50" stroke="url(#gradient)" stroke-width="0.5" fill="none"/>
        <path class="path path-2" d="M0,70 Q30,40 50,70 T100,70" stroke="url(#gradient)" stroke-width="0.5" fill="none"/>
        <path class="path path-3" d="M0,30 Q30,10 50,30 T100,30" stroke="url(#gradient)" stroke-width="0.5" fill="none"/>
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="var(--color-primary)" stop-opacity="0.2"/>
            <stop offset="100%" stop-color="var(--color-accent)" stop-opacity="0.6"/>
          </linearGradient>
        </defs>
      </svg>
    </div>

    <!-- 数据流动点 -->
    <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
      <div class="data-point absolute w-2 h-2 bg-primary rounded-full" style="top:10%;left:20%;animation-delay:0s;"></div>
      <div class="data-point absolute w-2 h-2 bg-primary rounded-full" style="top:30%;left:50%;animation-delay:0.2s;"></div>
      <div class="data-point absolute w-2 h-2 bg-primary rounded-full" style="top:50%;left:15%;animation-delay:0.4s;"></div>
      <div class="data-point absolute w-2 h-2 bg-primary rounded-full" style="top:70%;left:60%;animation-delay:0.6s;"></div>
      <div class="data-point absolute w-2 h-2 bg-primary rounded-full" style="top:20%;left:80%;animation-delay:0.8s;"></div>
      <div class="data-point absolute w-2 h-2 bg-primary rounded-full" style="top:80%;left:30%;animation-delay:1.0s;"></div>
      <div class="data-point absolute w-2 h-2 bg-primary rounded-full" style="top:40%;left:70%;animation-delay:1.2s;"></div>
      <div class="data-point absolute w-2 h-2 bg-primary rounded-full" style="top:60%;left:40%;animation-delay:1.4s;"></div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';

// 删除动态生成位置的函数

// 组件挂载后，所有CSS动画会自动开始
onMounted(() => {
  // 组件挂载完成，CSS动画已自动启动
  // 这里可以添加一些其他逻辑，但不需要显式启动CSS动画
});
</script>

<style scoped>
/* 淡入下落动画 */
.fade-in-down {
  animation: fadeInDown 1.2s ease-out forwards;
}

/* 淡入上升动画 */
.fade-in-up {
  animation: fadeInUp 1s ease-out forwards;
}

/* 从右侧滑入 */
.slide-in-right {
  opacity: 0;
  transform: translateX(100px);
  animation: slideInRight 0.8s ease-out forwards;
  animation-delay: var(--delay, 0s);
}

/* 数据点动画 */
.data-point {
  opacity: 0;
  animation: floatAround 6s infinite alternate ease-in-out;
  animation-delay: var(--delay, 0s);
}

/* 路径动画 */
.path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
}

.path-1 {
  animation: drawPath 5s infinite alternate ease-in-out;
}

.path-2 {
  animation: drawPath 6s infinite alternate ease-in-out;
  animation-delay: 0.5s;
}

.path-3 {
  animation: drawPath 7s infinite alternate ease-in-out;
  animation-delay: 1s;
}

/* 热点脉冲效果 */
.ping-effect {
  animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* 特性项目样式 */
.feature-item {
  border-left: 4px solid transparent;
  transition: all 0.3s ease;
}

.feature-item:hover {
  border-left: 4px solid var(--color-primary);
  transform: translateX(5px);
}

/* 关键帧定义 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes floatAround {
  0% {
    opacity: 0;
    transform: translate(0, 0) scale(0.2);
  }
  20% {
    opacity: 0.7;
    transform: translate(var(--tx, -150px), var(--ty, -150px)) scale(1.5);
  }
  80% {
    opacity: 0.7;
    transform: translate(var(--tx, 150px), var(--ty, 150px)) scale(1.5);
  }
  100% {
    opacity: 0;
    transform: translate(0, 0) scale(0.2);
  }
}

@keyframes drawPath {
  0% {
    stroke-dashoffset: 1000;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes ping {
  0% {
    transform: scale(0.2);
    opacity: 0.8;
  }
  80%, 100% {
    transform: scale(1.5);
    opacity: 0;
  }
}
</style>