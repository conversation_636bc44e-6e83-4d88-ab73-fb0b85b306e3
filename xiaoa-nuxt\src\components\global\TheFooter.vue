<template>
  <footer class="footer bg-dark py-16 relative border-t border-white/10">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
        <div class="lg:col-span-1">
          <div class="footer-info">
            <h3 class="text-2xl font-bold mb-5 text-secondary">
              <img src="https://xiao-dr.oss-cn-beijing.aliyuncs.com/img/ed71b025db069bfdff54044d153d992.png"
                  alt="数字人科技" class="h-10">
            </h3>
            <p class="text-light/80 text-sm leading-relaxed">引领未来交互方式，打造智能数字世界</p>
            <p class="mt-4 text-light/80 text-sm">© {{ currentYear }} 长沙小艾引擎科技有限公司 版权所有</p>
          </div>
        </div>
        
        <div class="footer-links">
          <h4 class="text-base font-semibold mb-5 text-light">产品</h4>
          <ul class="list-none p-0 m-0">
            <li class="mb-3">
              <nuxt-link to="/#digital-human" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">数字人</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="/#distribution" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">分发系统</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="/#ai-agent" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">智能体</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="/#mini-app" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">客户端</nuxt-link>
            </li>
          </ul>
        </div>
        
        <div class="footer-links">
          <h4 class="text-base font-semibold mb-5 text-light">关于我们</h4>
          <ul class="list-none p-0 m-0">
            <li class="mb-3">
              <nuxt-link to="/#about-company" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">公司简介</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="#" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">团队介绍</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="#" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">新闻动态</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="#" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">加入我们</nuxt-link>
            </li>
          </ul>
        </div>
        
        <div class="footer-links">
          <h4 class="text-base font-semibold mb-5 text-light">支持</h4>
          <ul class="list-none p-0 m-0">
            <li class="mb-3">
              <nuxt-link to="#" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">帮助中心</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="#" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">文档</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="#" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">API</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="#" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">常见问题</nuxt-link>
            </li>
          </ul>
        </div>
        
        <div class="footer-links">
          <h4 class="text-base font-semibold mb-5 text-light">法律</h4>
          <ul class="list-none p-0 m-0">
            <li class="mb-3">
              <nuxt-link to="#" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">隐私政策</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="#" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">服务条款</nuxt-link>
            </li>
            <li class="mb-3">
              <nuxt-link to="#" class="text-white/70 no-underline text-sm hover:text-secondary hover:pl-1 transition-all duration-300">版权声明</nuxt-link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// 获取当前年份
const currentYear = computed(() => new Date().getFullYear());
</script>